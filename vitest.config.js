/**
 * Vitest Configuration for LiveKit Agents Testing
 * Optimized configuration for unit, integration, and performance tests
 */

import { defineConfig } from "vitest/config";

export default defineConfig({
  test: {
    // Global test configuration
    globals: true,
    environment: "node",

    // Test file patterns
    include: ["test/**/*.test.js", "test/**/*.spec.js"],
    exclude: ["node_modules/**", "dist/**", "build/**", ".git/**"],

    // Timeouts
    testTimeout: 30000,
    hookTimeout: 10000,

    // Setup files
    setupFiles: ["./test/setup.js"],

    // Coverage configuration
    coverage: {
      provider: "v8",
      reporter: ["text", "json", "html", "lcov"],
      reportsDirectory: "./test-reports/coverage",
      include: ["src/**/*.js"],
      exclude: [
        "src/**/*.test.js",
        "src/**/*.spec.js",
        "test/**",
        "scripts/**",
        "node_modules/**",
      ],
      thresholds: {
        global: {
          branches: 70,
          functions: 75,
          lines: 80,
          statements: 80,
        },
      },
    },

    // Reporters
    reporters: ["default", "verbose", "json"],

    // Parallel execution
    pool: "threads",
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1,
      },
    },

    // Retry configuration
    retry: 2,

    // Watch mode configuration
    watch: false,

    // Environment variables
    env: {
      NODE_ENV: "test",
      VITEST: "true",
    },

    // Test categorization
    sequence: {
      concurrent: true,
      shuffle: false,
    },

    // Performance optimizations
    isolate: true,
    passWithNoTests: false,

    // Custom test patterns for different test types
    projects: [
      {
        test: {
          name: "unit",
          include: ["test/unit/**/*.test.js"],
          testTimeout: 10000,
          pool: "threads",
        },
      },
      {
        test: {
          name: "integration",
          include: ["test/integration/**/*.test.js"],
          testTimeout: 20000,
          pool: "threads",
          poolOptions: {
            threads: {
              maxThreads: 2, // Limit threads for integration tests
            },
          },
        },
      },
      {
        test: {
          name: "performance",
          include: ["test/performance/**/*.test.js"],
          testTimeout: 60000,
          pool: "threads",
          poolOptions: {
            threads: {
              singleThread: true, // Run performance tests in single thread
            },
          },
        },
      },
    ],
  },

  // Build configuration for testing
  esbuild: {
    target: "node18",
  },

  // Define configuration
  define: {
    __TEST__: true,
    __VERSION__: JSON.stringify(process.env.npm_package_version || "1.0.0"),
  },
});
