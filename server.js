import express from "express";
import { AccessToken } from "livekit-server-sdk";
import { config } from "dotenv";
import path from "path";
import { fileURLToPath } from "url";
import { createSimpleLogger } from "./src/simple-logger.js";

// 加载环境变量
config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize simple logger
const logger = createSimpleLogger("Server");

// 修复内存泄漏警告
process.setMaxListeners(20);

// 中间件
app.use(express.json());
app.use(express.static("public"));

// 简化的请求日志中间件
app.use((req, res, next) => {
  if (req.path.startsWith("/api/")) {
    logger.debug(`${req.method} ${req.path}`);
  }
  next();
});

// API 路由：生成访问令牌
app.post("/api/token", async (req, res) => {
  try {
    const { roomName, participantName } = req.body;

    if (!roomName || !participantName) {
      logger.warn("Token request missing parameters");

      return res.status(400).json({
        error: "缺少必要参数: roomName 和 participantName",
      });
    }

    // 创建访问令牌
    const token = new AccessToken(
      process.env.LIVEKIT_API_KEY,
      process.env.LIVEKIT_API_SECRET,
      {
        identity: participantName,
        ttl: "10m", // 10分钟有效期
      }
    );

    // 添加房间权限
    token.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
      canPublishData: true,
    });

    const jwt = await token.toJwt();

    res.json({
      token: jwt,
      url: process.env.LIVEKIT_URL,
    });

    logger.debug("Access token generated", { participantName, roomName });
  } catch (error) {
    logger.error("Token generation failed", { error: error.message });

    // 简化错误跟踪

    res.status(500).json({
      error: "生成访问令牌失败",
    });
  }
});

// Import monitoring modules
import healthChecker from "./src/monitoring/health.js";
import metricsCollector from "./src/monitoring/metrics.js";

// 健康检查端点
app.get("/api/health", async (req, res) => {
  try {
    const health = await healthChecker.getSimpleHealth();
    res.status(health.status === "ok" ? 200 : 503).json({
      ...health,
      architecture: "LiveKit Agent + Deepgram STT",
      environment: {
        livekit_url: process.env.LIVEKIT_URL,
        livekit_configured: !!(
          process.env.LIVEKIT_API_KEY && process.env.LIVEKIT_API_SECRET
        ),
      },
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      timestamp: new Date().toISOString(),
      error: error.message,
    });
  }
});

// 详细健康检查端点
app.get("/api/health/detailed", async (req, res) => {
  try {
    const health = await healthChecker.getDetailedHealth();
    res.status(health.healthy ? 200 : 503).json(health);
  } catch (error) {
    res.status(500).json({
      healthy: false,
      timestamp: new Date().toISOString(),
      error: error.message,
    });
  }
});

// 就绪检查端点 (Kubernetes readiness probe)
app.get("/api/health/ready", async (req, res) => {
  try {
    const ready = await healthChecker.isReady();
    res.status(ready ? 200 : 503).json({
      ready,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(503).json({
      ready: false,
      timestamp: new Date().toISOString(),
      error: error.message,
    });
  }
});

// 存活检查端点 (Kubernetes liveness probe)
app.get("/api/health/live", async (req, res) => {
  try {
    const alive = await healthChecker.isAlive();
    res.status(alive ? 200 : 503).json({
      alive,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(503).json({
      alive: false,
      timestamp: new Date().toISOString(),
      error: error.message,
    });
  }
});

// Prometheus 指标端点
app.get("/api/metrics", (req, res) => {
  try {
    const metrics = metricsCollector.getPrometheusMetrics();
    res.set("Content-Type", "text/plain; version=0.0.4; charset=utf-8");
    res.send(metrics);
  } catch (error) {
    res.status(500).json({
      error: "Failed to generate metrics",
      message: error.message,
    });
  }
});

// 指标 JSON 端点
app.get("/api/metrics/json", (req, res) => {
  try {
    const metrics = metricsCollector.getHealthMetrics();
    res.json(metrics);
  } catch (error) {
    res.status(500).json({
      error: "Failed to get metrics",
      message: error.message,
    });
  }
});

// 根路由
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "public", "index.html"));
});

// Agent 测试页面
app.get("/agent-test", (req, res) => {
  res.sendFile(path.join(__dirname, "public", "agent-test.html"));
});

// 简单测试路由
app.get("/test", (req, res) => {
  res.json({
    message: "服务器工作正常",
    timestamp: new Date().toISOString(),
    routes: ["/", "/agent-test", "/api/health"],
  });
});

// 调试路由 - 检查文件系统
app.get("/debug", (req, res) => {
  const fs = require("fs");
  const path = require("path");

  try {
    const publicDir = path.join(__dirname, "public");
    const files = fs.readdirSync(publicDir);

    res.json({
      message: "调试信息",
      currentDir: __dirname,
      publicDir: publicDir,
      publicExists: fs.existsSync(publicDir),
      files: files,
      statusHtmlExists: fs.existsSync(path.join(publicDir, "status.html")),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      error: error.message,
      currentDir: __dirname,
    });
  }
});

// 内联状态检查页面
app.get("/inline-status", (req, res) => {
  const html = `
<!DOCTYPE html>
<html>
<head>
    <title>内联状态检查</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .status { padding: 15px; margin: 15px 0; border-radius: 5px; font-weight: bold; }
        .ok { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow: auto; }
        button { padding: 12px 24px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 LiveKit Agents 状态检查</h1>

        <div id="serverStatus" class="status info">检查服务器状态...</div>
        <div id="configStatus" class="status info">检查配置...</div>
        <div id="tokenStatus" class="status info">检查令牌生成...</div>
        <div id="agentStatus" class="status info">检查 Agent 状态...</div>

        <div style="text-align: center; margin: 20px 0;">
            <button onclick="runTests()">运行所有测试</button>
            <button onclick="location.reload()">刷新页面</button>
        </div>

        <h3>详细日志：</h3>
        <pre id="log">点击"运行所有测试"开始...</pre>
    </div>

    <script>
        function log(message) {
            const logEl = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logEl.textContent += '[' + time + '] ' + message + '\\n';
        }

        function updateStatus(id, message, type) {
            const el = document.getElementById(id);
            el.textContent = message;
            el.className = 'status ' + type;
        }

        async function runTests() {
            document.getElementById('log').textContent = '';
            log('🚀 开始运行测试...');

            // 测试 1: 服务器健康检查
            try {
                log('1. 测试服务器健康检查...');
                const response = await fetch('/api/health');
                const data = await response.json();
                updateStatus('serverStatus', '✅ 服务器运行正常', 'ok');
                log('✅ 服务器健康检查通过');
                log('服务器状态: ' + JSON.stringify(data, null, 2));
            } catch (error) {
                updateStatus('serverStatus', '❌ 服务器连接失败', 'error');
                log('❌ 服务器健康检查失败: ' + error.message);
            }

            // 测试 2: 配置检查
            try {
                log('\\n2. 检查 LiveKit Agent 配置...');
                updateStatus('configStatus', '✅ LiveKit Agent 模式', 'ok');
                log('✅ 使用 LiveKit Agent + Deepgram 架构');
                log('配置: Node.js Agent 处理语音转录');
            } catch (error) {
                updateStatus('configStatus', '❌ 配置检查失败', 'error');
                log('❌ 配置检查失败: ' + error.message);
            }

            // 测试 3: 令牌生成
            try {
                log('\\n3. 测试令牌生成...');
                const response = await fetch('/api/token', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        roomName: 'test-room-' + Date.now(),
                        participantName: 'test-user-' + Date.now()
                    })
                });
                const data = await response.json();
                updateStatus('tokenStatus', '✅ 令牌生成正常', 'ok');
                log('✅ 令牌生成成功');
                log('令牌信息: ' + JSON.stringify(data, null, 2));
            } catch (error) {
                updateStatus('tokenStatus', '❌ 令牌生成失败', 'error');
                log('❌ 令牌生成失败: ' + error.message);
            }

            // 测试 4: Agent 状态
            try {
                log('\\n4. 检查 Agent 状态...');
                updateStatus('agentStatus', '⚠️ Agent 状态需要手动验证', 'info');
                log('⚠️ Agent 状态检查:');
                log('- 确保运行: node minimal-agent.js');
                log('- Agent 会在有参与者加入房间时自动连接');
                log('- 查看 Agent 终端输出以确认状态');
            } catch (error) {
                updateStatus('agentStatus', '❌ Agent 状态检查失败', 'error');
                log('❌ Agent 状态检查失败: ' + error.message);
            }

            log('\\n🎉 测试完成！');
            log('\\n💡 下一步:');
            log('1. 如果所有测试都通过，系统准备就绪');
            log('2. 可以使用 LiveKit 客户端连接进行实际测试');
            log('3. Agent 会在参与者加入时自动开始转录');
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            setTimeout(runTests, 1000);
        };
    </script>
</body>
</html>
  `;
  res.send(html);
});

// 超简单测试页面
app.get("/simple", (req, res) => {
  res.send(`
    <html>
    <head><title>简单测试</title></head>
    <body style="font-family: Arial; padding: 20px;">
      <h1>LiveKit Agents 简单测试</h1>
      <div id="result">测试中...</div>
      <script>
        fetch('/api/health')
          .then(r => r.json())
          .then(data => {
            document.getElementById('result').innerHTML =
              '<h2>✅ 服务器工作正常</h2>' +
              '<pre>' + JSON.stringify(data, null, 2) + '</pre>' +
              '<p>使用 LiveKit Agent + Deepgram 架构</p>' +
              '<p><a href="/debug">查看调试信息</a></p>';
          })
          .catch(err => {
            document.getElementById('result').innerHTML =
              '<h2>❌ 服务器错误</h2><p>' + err.message + '</p>';
          });
      </script>
    </body>
    </html>
  `);
});

// 启动服务器
app.listen(PORT, () => {
  logger.info("Server started successfully", {
    port: PORT,
    url: `http://localhost:${PORT}`,
  });

  if (!process.env.LIVEKIT_API_KEY || !process.env.LIVEKIT_API_SECRET) {
    logger.warn("LiveKit API keys not configured - check .env file");
  }
});
