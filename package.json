{"name": "livekit-agents-deepgram", "version": "1.0.0", "description": "LiveKit Agents + Deepgram 实时语音转录 - 企业级多人对话分类和多语言识别", "main": "server.js", "type": "module", "scripts": {"start": "LOG_LEVEL=INFO node server.js", "dev": "LOG_LEVEL=DEBUG nodemon server.js", "agent": "node src/enhanced-agent-v2.js start", "agent:dev": "node src/enhanced-agent-v2.js dev", "agent:debug": "node debug-agent.js dev", "setup": "./scripts/setup-env.sh", "validate": "node scripts/validate-config.js", "validate:quick": "node scripts/validate-config.js --skip-connectivity", "health": "curl -s http://localhost:3000/api/health | jq '.'", "test": "vitest", "test:run": "vitest run", "test:unit": "vitest run test/unit", "test:integration": "vitest run test/integration", "test:performance": "vitest run test/performance", "test:all": "node test/test-runner.js", "test:coverage": "vitest run --coverage", "test:watch": "vitest", "test:error-handler": "vitest run test/error-handler.test.js", "test:health": "node scripts/health-check.js", "test:agent": "node scripts/agent-health-check.js"}, "keywords": ["livekit", "deepgram", "speech-to-text", "transcription", "realtime"], "author": "", "license": "MIT", "dependencies": {"@livekit/agents": "^0.7.7", "@livekit/agents-plugin-deepgram": "^0.5.0", "@livekit/agents-plugin-openai": "^0.9.3", "@livekit/rtc-node": "^0.13.18", "dotenv": "^16.3.1", "express": "^4.18.2", "livekit-server-sdk": "^2.0.0"}, "devDependencies": {"@types/express": "^4.17.20", "@types/node": "^20.8.0", "@types/ws": "^8.5.8", "typescript": "^5.2.2", "vitest": "^3.2.4"}}