#!/usr/bin/env node

/**
 * 简化的启动脚本
 * 类似参考项目的 start.py
 */

import { spawn } from "child_process";
import { fileURLToPath } from "url";
import path from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function startWeb() {
  console.log("🌐 启动 Web 服务器...");
  const webProcess = spawn("node", ["server.js"], {
    cwd: __dirname,
    stdio: "inherit",
  });

  webProcess.on("error", (error) => {
    console.error("❌ Web 服务器启动失败:", error);
    process.exit(1);
  });

  webProcess.on("exit", (code) => {
    console.log(`🌐 Web 服务器已退出，退出码: ${code}`);
    process.exit(code);
  });
}

function startAgent() {
  console.log("🤖 启动转录 Agent...");
  const agentProcess = spawn("node", ["src/simple-agent.js", "start"], {
    cwd: __dirname,
    stdio: "inherit",
  });

  agentProcess.on("error", (error) => {
    console.error("❌ Agent 启动失败:", error);
    process.exit(1);
  });

  agentProcess.on("exit", (code) => {
    console.log(`🤖 Agent 已退出，退出码: ${code}`);
    process.exit(code);
  });
}

function startMultimodalAgent() {
  console.log("🤖 启动 MultimodalAgent 转录服务...");
  const agentProcess = spawn("node", ["src/multimodal-agent.js", "start"], {
    cwd: __dirname,
    stdio: "inherit",
  });

  agentProcess.on("error", (error) => {
    console.error("❌ MultimodalAgent 启动失败:", error);
    process.exit(1);
  });

  agentProcess.on("exit", (code) => {
    console.log(`🤖 MultimodalAgent 已退出，退出码: ${code}`);
    process.exit(code);
  });
}

function startTestAgent() {
  console.log("🧪 启动测试Agent...");
  const agentProcess = spawn("node", ["src/test-agent.js", "start"], {
    cwd: __dirname,
    stdio: "inherit",
  });

  agentProcess.on("error", (error) => {
    console.error("❌ 测试Agent 启动失败:", error);
    process.exit(1);
  });

  agentProcess.on("exit", (code) => {
    console.log(`🧪 测试Agent 已退出，退出码: ${code}`);
    process.exit(code);
  });
}

function startDirectAgent() {
  console.log("🎯 启动直接转录Agent...");
  const agentProcess = spawn("node", ["src/direct-agent.js"], {
    cwd: __dirname,
    stdio: "inherit",
  });

  agentProcess.on("error", (error) => {
    console.error("❌ 直接Agent 启动失败:", error);
    process.exit(1);
  });

  agentProcess.on("exit", (code) => {
    console.log(`🎯 直接Agent 已退出，退出码: ${code}`);
    process.exit(code);
  });
}

function startVoicePipelineAgent() {
  console.log("🎤 启动 VoicePipelineAgent 转录服务...");
  const agentProcess = spawn("node", ["src/voice-pipeline-agent.js", "start"], {
    cwd: __dirname,
    stdio: "inherit",
  });

  agentProcess.on("error", (error) => {
    console.error("❌ VoicePipelineAgent 启动失败:", error);
    process.exit(1);
  });

  agentProcess.on("exit", (code) => {
    console.log(`🎤 VoicePipelineAgent 已退出，退出码: ${code}`);
    process.exit(code);
  });
}

function startWorkingDeepgramAgent() {
  console.log("🔧 启动工作版Deepgram Agent...");
  const agentProcess = spawn(
    "node",
    ["src/working-deepgram-agent.js", "start"],
    {
      cwd: __dirname,
      stdio: "inherit",
    }
  );

  agentProcess.on("error", (error) => {
    console.error("❌ 工作版Deepgram Agent 启动失败:", error);
    process.exit(1);
  });

  agentProcess.on("exit", (code) => {
    console.log(`🔧 工作版Deepgram Agent 已退出，退出码: ${code}`);
    process.exit(code);
  });
}

function showHelp() {
  console.log(`
🚀 LiveKit + Deepgram 实时语音转录系统

使用方法:
  node start.js web              - 启动 Web 服务器
  node start.js agent            - 启动转录 Agent (原版)
  node start.js multimodal       - 启动 MultimodalAgent 转录服务
  node start.js voice            - 启动 VoicePipelineAgent 转录服务
  node start.js working          - 启动工作版Deepgram Agent (推荐)
  node start.js direct           - 启动直接转录Agent
  node start.js test             - 启动测试Agent (调试用)
  node start.js help             - 显示此帮助信息

完整启动流程:
  1. 终端1: node start.js web
  2. 终端2: node start.js working  (推荐使用工作版Deepgram Agent)
  3. 访问: http://localhost:3000

注意: 如果端口3000被占用，Web服务器会自动使用端口5000
`);
}

// 主程序入口
const command = process.argv[2]?.toLowerCase();

if (!command) {
  showHelp();
  process.exit(1);
}

switch (command) {
  case "web":
    startWeb();
    break;
  case "agent":
    startAgent();
    break;
  case "multimodal":
    startMultimodalAgent();
    break;
  case "voice":
    startVoicePipelineAgent();
    break;
  case "working":
    startWorkingDeepgramAgent();
    break;
  case "direct":
    startDirectAgent();
    break;
  case "test":
    startTestAgent();
    break;
  case "help":
    showHelp();
    break;
  default:
    console.log(`❌ 未知命令: ${command}`);
    showHelp();
    process.exit(1);
}
