# LiveKit Agents + Deepgram 实时语音转录

基于 LiveKit Agents 和 Deepgram 的实时语音转录系统，支持多人对话和说话人识别。

## 🌟 主要特性

- **🎤 实时语音转录**: 基于 Deepgram 的高精度语音识别
- **👥 多人对话支持**: 智能说话人识别和分类
- **🌍 多语言识别**: 支持多种语言的实时转录
- **🛡️ 错误处理**: 完善的错误处理和恢复机制

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，填入你的 API 密钥
```

### 3. 启动系统

```bash
# 启动 Web 服务器
npm start

# 启动转录代理（新终端）
npm run agent

# 或者开发模式（更多日志）
npm run agent:dev
```

### 4. 访问应用

打开浏览器访问：`http://localhost:3000`

## ⚙️ 环境配置

在 `.env` 文件中配置以下变量：

```bash
# LiveKit 配置
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-api-secret

# Deepgram 配置
DEEPGRAM_API_KEY=your-deepgram-api-key

# 服务器配置
PORT=3000
NODE_ENV=development
```

## 🔧 可用命令

```bash
# 开发
npm start          # 启动 Web 服务器
npm run agent      # 启动转录代理（生产模式）
npm run agent:dev  # 启动转录代理（开发模式）
npm run dev        # Web服务器开发模式（自动重启）

# 配置和测试
npm run validate   # 验证配置
npm run health     # 健康检查
npm test          # 运行测试

# 工具
npm run setup     # 环境设置向导
```

## 📁 项目结构

```
├── src/
│   ├── enhanced-agent-v2.js      # 主转录代理
│   ├── audio-stream-processor.js # 音频流处理
│   ├── error-handler.js          # 错误处理
│   └── ...
├── public/                       # 前端文件
├── test/                         # 测试文件
├── scripts/                      # 工具脚本
└── server.js                     # Web 服务器
```

## 🔍 故障排除

### 常见问题

1. **代理无法启动**: 运行 `npm run validate` 检查配置
2. **音频连接失败**: 检查 LiveKit 服务器连接和 API 密钥
3. **转录不准确**: 确认音频质量和语言设置

### 查看日志

```bash
# 查看代理日志
npm run agent

# 健康检查
npm run health
```

## 📄 许可证

MIT License

---

**详细文档请查看 [项目说明.md](./项目说明.md)**
