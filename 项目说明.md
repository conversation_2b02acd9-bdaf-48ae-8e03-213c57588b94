# LiveKit Agents + Deepgram 实时语音转录系统

## 📋 项目简介

这是一个基于 LiveKit Agents 和 Deepgram 的实时语音转录系统，支持多人对话、说话人识别和多语言转录。系统采用服务端架构，提供稳定可靠的实时语音转录服务。

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入你的 API 密钥
```

### 2. 启动系统

```bash
# 启动 Web 服务器
npm start

# 启动转录代理（新终端窗口）
npm run agent

# 或者开发模式（更多日志）
npm run agent:dev
```

### 3. 访问系统

打开浏览器访问：`http://localhost:3000`

## 📁 项目结构

```
livekit_deepgram/
├── src/                              # 源代码
│   ├── enhanced-agent-v2.js         # 🚀 主转录代理
│   ├── audio-stream-processor.js    # 🎵 音频流处理器
│   ├── config-validator.js          # ⚙️ 配置验证器
│   ├── error-handler.js             # 🛡️ 错误处理系统
│   ├── logging/                     # 📝 日志系统
│   └── monitoring/                  # 📊 监控系统
├── public/                          # 前端文件
│   ├── index.html                   # 主页面
│   ├── app.js                       # 前端逻辑
│   ├── error-display-manager.js     # 错误显示管理
│   ├── transcription-data-manager.js # 转录数据管理
│   └── transcription-ui-manager.js  # 转录界面管理
├── test/                            # 测试文件
├── scripts/                         # 工具脚本
├── server.js                        # 🌐 Web服务器
└── package.json                     # 项目配置
```

## ⚙️ 环境配置

### 必需的环境变量

在 `.env` 文件中配置以下变量：

```bash
# LiveKit 配置
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-api-secret

# Deepgram 配置
DEEPGRAM_API_KEY=your-deepgram-api-key

# 服务器配置
PORT=3000
NODE_ENV=development
```

### 配置验证

```bash
# 验证配置是否正确
npm run validate

# 快速验证（跳过连接测试）
npm run validate:quick
```

## 🎯 核心功能

### 🎤 实时语音转录

- 支持多人同时说话
- 实时显示转录结果
- 区分临时结果和最终结果

### 👥 说话人识别

- 自动识别不同说话人
- 可视化显示说话人标识
- 支持多人对话场景

### 🌍 多语言支持

- 支持多种语言的语音识别
- 自动语言检测
- 可配置目标语言

### 🛡️ 错误处理

- 智能错误恢复机制
- 用户友好的错误提示
- 自动重试和降级策略

### 📊 监控和日志

- 简洁的日志输出，只显示重要信息
- 彩色日志便于开发调试
- 可配置的日志级别

## 🔧 开发工具

### 启动命令

```bash
# Web 服务器
npm start          # 启动 Web 服务器（简洁日志）
npm run dev        # Web服务器开发模式（详细日志 + 自动重启）

# 转录代理
npm run agent      # 启动转录代理（生产模式）
npm run agent:dev  # 启动转录代理（开发模式，更多日志）
```

### 测试命令

```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行性能测试
npm run test:performance

# 错误处理测试
npm run test:error-handler
```

### 健康检查

```bash
# 检查系统健康状态
npm run test:health

# 检查代理状态
npm run test:agent

# Web API 健康检查
npm run health
```

## 🏗️ 系统架构

### 数据流

```
用户浏览器 → LiveKit Client → LiveKit Cloud → Node.js Agent → Deepgram STT
                                                        ↓
用户浏览器 ← 转录结果 ← LiveKit Cloud ← 处理完成 ← Deepgram API
```

### 核心组件

#### 🚀 Enhanced Agent v2

- **多策略初始化**: 支持多种连接策略的自动降级
- **Beta 版本兼容**: 处理 LiveKit Agents beta 版本的已知问题
- **结构化日志**: 完整的日志记录和错误跟踪
- **性能监控**: 实时收集连接和转录指标

#### 🎵 音频流处理器

- **多连接策略**: 4 种不同的音频连接方式
- **智能重试**: 指数退避重试机制
- **错误恢复**: 自动处理连接失败和恢复

#### 🛡️ 错误处理系统

- **分类错误**: 20+种特定错误类型
- **严重级别**: 4 个不同的错误严重级别
- **用户友好**: 自动生成中文错误消息和解决建议

## 🔍 故障排除

### 常见问题

#### 1. 代理无法启动

```bash
# 检查环境变量配置
npm run validate

# 查看详细错误日志
npm run agent
```

#### 2. 音频连接失败

- 检查 LiveKit 服务器连接
- 验证 API 密钥是否正确
- 确认网络连接正常

#### 3. 转录不准确

- 检查音频质量
- 确认语言设置正确
- 验证 Deepgram API 配置

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 查看代理日志
npm run agent  # 控制台输出
```

## 📈 性能优化

### 系统要求

- Node.js 18+
- 内存: 最少 512MB
- 网络: 稳定的互联网连接

### 优化建议

- 使用稳定的网络环境
- 确保足够的系统资源
- 定期清理日志文件
- 监控系统性能指标

## 🔒 安全考虑

### API 密钥管理

- 使用环境变量存储敏感信息
- 定期轮换 API 密钥
- 不要在代码中硬编码密钥

### 网络安全

- 使用 HTTPS 连接
- 验证 WebSocket 连接安全性
- 实施适当的访问控制

## 📝 更新日志

### v1.0.0 (当前版本)

- ✅ 完整的实时语音转录功能
- ✅ 多人对话支持
- ✅ 说话人识别
- ✅ 错误处理和恢复机制
- ✅ 生产级日志系统
- ✅ 监控和健康检查

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 📞 支持

如果遇到问题，请：

1. 查看故障排除部分
2. 检查日志文件
3. 运行健康检查命令
4. 提交 Issue

---

**🎯 这是一个生产就绪的实时语音转录系统，具备完整的错误处理、监控和日志功能！**
