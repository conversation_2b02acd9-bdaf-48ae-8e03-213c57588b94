<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LiveKit + Deepgram 实时音频转文本</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .main-content {
        padding: 40px 30px;
      }

      .controls {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-bottom: 20px;
        flex-wrap: wrap;
      }

      .model-controls {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
        border: 1px solid #e9ecef;
      }

      .model-group,
      .config-info {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .model-select {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background: white;
        font-size: 14px;
        min-width: 180px;
      }

      .config-status {
        background: #f3e5f5;
        color: #7b1fa2;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
      }

      .btn {
        padding: 15px 30px;
        border: none;
        border-radius: 25px;
        font-size: 1.1em;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 150px;
      }

      .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
      }

      .btn-danger {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
      }

      .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
      }

      .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      /* 转录控制区域 */
      .transcription-controls {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
      }

      .control-row {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      /* 紧凑按钮样式 */
      .btn-compact {
        padding: 8px 16px;
        border-radius: 6px;
        border: 1px solid #ddd;
        background: #fff;
        color: #333;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 6px;
      }

      .btn-compact:hover {
        background: #f8f9fa;
        border-color: #adb5bd;
      }

      .btn-compact:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .btn-compact .btn-icon {
        font-size: 14px;
      }

      .btn-compact .btn-text {
        font-size: 14px;
        font-weight: 500;
      }

      /* 文本状态样式 */
      .status-text {
        font-size: 14px;
        color: #666;
        font-weight: 500;
        padding: 4px 8px;
        background: #e9ecef;
        border-radius: 4px;
        min-width: 60px;
        text-align: center;
      }

      .status-text.ready {
        color: #28a745;
        background: #d4edda;
      }

      .status-text.connecting {
        color: #856404;
        background: #fff3cd;
      }

      .status-text.recording {
        color: #721c24;
        background: #f8d7da;
        font-weight: 600;
        min-width: 80px;
      }

      .status-text.error {
        color: #721c24;
        background: #f8d7da;
      }

      /* 紧凑按钮状态样式 */
      .btn-compact[data-state="ready"] {
        background: #007bff;
        color: white;
        border-color: #007bff;
      }

      .btn-compact[data-state="connecting"] {
        background: #ffc107;
        color: #333;
        border-color: #ffc107;
      }

      .btn-compact[data-state="recording"] {
        background: #dc3545;
        color: white;
        border-color: #dc3545;
      }

      .btn-compact[data-state="error"] {
        background: #6c757d;
        color: white;
        border-color: #6c757d;
      }

      /* ChatUI 风格样式 */
      .chat-container {
        background: #fff;
        border: 1px solid #e1e5e9;
        border-radius: 12px;
        margin-top: 20px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .chat-header {
        background: #f8f9fa;
        padding: 16px 20px;
        border-bottom: 1px solid #e1e5e9;
      }

      .chat-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
      }

      .chat-messages {
        height: 400px;
        overflow-y: auto;
        padding: 20px;
        background: #fafbfc;
        scroll-behavior: smooth;
      }

      .welcome-message {
        text-align: center;
        margin: 40px 0;
      }

      .system-message {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: #e3f2fd;
        color: #1976d2;
        padding: 12px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
      }

      .message-icon {
        font-size: 16px;
      }

      .current-transcription {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 15px;
        border-radius: 5px;
        font-size: 1.1em;
        min-height: 60px;
        display: flex;
        align-items: center;
        font-style: italic;
        color: #1976d2;
      }

      .transcription-history {
        height: 300px;
        min-height: 300px;
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        padding: 10px;

        background: #fafafa;
        box-sizing: border-box;
      }

      .transcription-item {
        background: white;
        margin-bottom: 10px;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #4caf50;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .transcription-text {
        font-size: 1em;
        line-height: 1.5;
        margin-bottom: 8px;
      }

      .transcription-meta {
        font-size: 0.85em;
        color: #666;
        display: flex;
        justify-content: space-between;
      }

      .confidence {
        font-weight: bold;
      }

      .confidence.high {
        color: #4caf50;
      }
      .confidence.medium {
        color: #ff9800;
      }
      .confidence.low {
        color: #f44336;
      }

      /* 转录消息样式 */
      .transcription-message {
        margin-bottom: 16px;
      }

      .message-bubble {
        background: #fff;
        border: 1px solid #e1e5e9;
        border-radius: 12px;
        padding: 12px 16px;
        max-width: 80%;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      /* 临时消息样式 */
      .interim-message .interim-bubble {
        background: #f8f9fa;
        border: 1px dashed #6c757d;
        border-radius: 12px;
        padding: 12px 16px;
        max-width: 80%;
        box-shadow: none;
        position: relative;
      }

      .interim-content {
        display: flex;
        align-items: center;
        font-style: italic;
        color: #6c757d;
      }

      .interim-row {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
      }

      .wave-animation {
        display: flex;
        align-items: center;
        gap: 2px;
        height: 16px;
      }

      .wave-bar {
        width: 3px;
        background: #007bff;
        border-radius: 2px;
        animation: wave-bounce 1.2s ease-in-out infinite;
      }

      .wave-bar:nth-child(1) {
        height: 8px;
        animation-delay: 0s;
      }

      .wave-bar:nth-child(2) {
        height: 12px;
        animation-delay: 0.2s;
      }

      .wave-bar:nth-child(3) {
        height: 6px;
        animation-delay: 0.4s;
      }

      @keyframes wave-bounce {
        0%,
        100% {
          transform: scaleY(0.5);
          opacity: 0.7;
        }
        50% {
          transform: scaleY(1);
          opacity: 1;
        }
      }

      .interim-text {
        font-size: 14px;
        flex: 1;
        color: #6c757d;
        font-style: italic;
      }

      /* 消息头部样式 */
      .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;
        font-size: 12px;
      }

      .speaker-info {
        display: flex;
        align-items: center;
        gap: 6px;
        font-weight: 600;
        color: #495057;
      }

      .speaker-icon {
        font-size: 14px;
      }

      .speaker-name {
        color: inherit;
      }

      .message-meta-inline {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 11px;
        color: #6c757d;
      }

      .timestamp {
        color: #6c757d;
      }

      /* 不同发言者的边框颜色 */
      .speaker-0 {
        border-left: 4px solid #007bff;
      }

      .speaker-1 {
        border-left: 4px solid #28a745;
      }

      .speaker-2 {
        border-left: 4px solid #ffc107;
      }

      .speaker-3 {
        border-left: 4px solid #dc3545;
      }

      .speaker-4 {
        border-left: 4px solid #6f42c1;
      }

      .speaker-default {
        border-left: 4px solid #6c757d;
      }

      .message-content {
        font-size: 14px;
        line-height: 1.5;
        color: #2c3e50;
        margin-bottom: 4px;
      }

      .message-meta {
        font-size: 12px;
        color: #6c757d;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .confidence-badge {
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 11px;
        font-weight: 500;
      }

      .confidence-badge.high {
        background: #d4edda;
        color: #155724;
      }

      .confidence-badge.medium {
        background: #fff3cd;
        color: #856404;
      }

      .confidence-badge.low {
        background: #f8d7da;
        color: #721c24;
      }

      .loading {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .pulse {
        animation: pulse 1.5s ease-in-out infinite alternate;
      }

      @keyframes pulse {
        from {
          opacity: 0.6;
        }
        to {
          opacity: 1;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="main-content">
        <!-- 配置状态显示 -->
        <div class="model-controls">
          <div class="config-info">
            <span id="configStatus" class="config-status">配置: 加载中...</span>
          </div>
        </div>

        <!-- 转录控制区域 -->
        <div class="transcription-controls">
          <div class="control-row">
            <button
              id="primaryActionBtn"
              class="btn btn-compact"
              data-state="ready"
            >
              <span class="btn-icon">🎤</span>
              <span class="btn-text">开始转录</span>
            </button>
            <span id="statusText" class="status-text">就绪</span>
          </div>
        </div>

        <!-- ChatUI 风格的转录区域 -->
        <div class="chat-container">
          <div class="chat-header">
            <h3>💬 语音转录</h3>
          </div>
          <div id="chatMessages" class="chat-messages">
            <div class="welcome-message">
              <div class="system-message">
                <span class="message-icon">🎤</span>
                <span class="message-text">准备开始语音转录...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载 LiveKit 客户端库 -->
    <script>
      // 改进的 LiveKit 客户端库加载函数
      function loadLiveKitClient() {
        return new Promise((resolve, reject) => {
          // 如果已经加载，直接返回
          if (typeof LivekitClient !== "undefined") {
            console.log("✅ LiveKit 客户端库已存在");
            resolve();
            return;
          }

          const cdnUrls = [
            "https://unpkg.com/livekit-client@2.5.0/dist/livekit-client.umd.js",
            "https://cdn.jsdelivr.net/npm/livekit-client@2.5.0/dist/livekit-client.umd.js",
            "https://unpkg.com/livekit-client@latest/dist/livekit-client.umd.js",
          ];

          let currentIndex = 0;

          function tryLoadScript() {
            if (currentIndex >= cdnUrls.length) {
              reject(new Error("所有 CDN 源都加载失败"));
              return;
            }

            const script = document.createElement("script");
            script.onload = () => {
              // 等待一小段时间确保库完全加载
              setTimeout(() => {
                if (typeof LivekitClient !== "undefined") {
                  console.log(
                    `✅ LiveKit 客户端库加载成功 (CDN ${currentIndex + 1})`
                  );
                  resolve();
                } else {
                  console.warn(
                    `⚠️ CDN ${currentIndex + 1} 加载后未找到 LivekitClient`
                  );
                  currentIndex++;
                  tryLoadScript();
                }
              }, 100);
            };

            script.onerror = () => {
              console.warn(
                `❌ CDN ${currentIndex + 1} 加载失败: ${cdnUrls[currentIndex]}`
              );
              currentIndex++;
              tryLoadScript();
            };

            script.src = cdnUrls[currentIndex];
            document.head.appendChild(script);
          }

          tryLoadScript();
        });
      }

      // 加载库
      loadLiveKitClient().catch((error) => {
        console.error("❌ LiveKit 客户端库加载失败:", error);
        // 显示友好的错误提示
        const status = document.querySelector(".status");
        if (status) {
          status.className = "status error";
          status.innerHTML =
            "⚠️ LiveKit 客户端库加载失败，请检查网络连接或刷新页面重试";
        }
      });
    </script>
    <!-- Error display management -->
    <script src="error-display-manager.js"></script>
    <!-- Enhanced transcription management scripts -->
    <script src="transcription-data-manager.js"></script>
    <script src="transcription-ui-manager.js"></script>
    <!-- 应用脚本 -->
    <script src="app.js"></script>
  </body>
</html>
