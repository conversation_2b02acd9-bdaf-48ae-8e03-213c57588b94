class TranscriptionApp {
  constructor() {
    this.transcriptions = [];
    this.sessionStartTime = null;
    this.sessionTimer = null;

    // Initialize enhanced transcription management
    this.dataManager = null;
    this.uiManager = null;

    this.initializeElements();
    this.bindEvents();
    this.initializeTranscriptionManagers();

    // 设置全局引用供转录模块使用
    window.transcriptionApp = this;
  }

  initializeElements() {
    // 优化后的控制按钮
    this.primaryActionBtn = document.getElementById("primaryActionBtn");
    this.statusText = document.getElementById("statusText");

    // 保留原有元素引用（向后兼容）
    this.connectBtn = this.primaryActionBtn; // 兼容性
    this.startTranscriptionBtn = this.primaryActionBtn; // 兼容性
    this.stopTranscriptionBtn = this.primaryActionBtn; // 兼容性

    this.currentTranscription = document.getElementById("currentTranscription");
    this.chatMessages = document.getElementById("chatMessages");

    // 初始化按钮状态
    this.updateButtonState("ready");
  }

  /**
   * Initialize enhanced transcription managers
   */
  initializeTranscriptionManagers() {
    console.log("🚀 Initializing enhanced transcription managers...");

    try {
      // Initialize data manager
      this.dataManager = new TranscriptionDataManager({
        maxInterimAge: 5000,
        maxHistorySize: 1000,
        enableSpeakerDiarization: true,
        enableMultiParticipant: true,
        confidenceThreshold: 0.5,
      });

      // Initialize UI manager
      this.uiManager = new TranscriptionUIManager(
        this.chatMessages.parentElement,
        {
          enableAnimations: true,
          showConfidence: true,
          showTimestamps: true,
          showSpeakerInfo: true,
          maxVisibleMessages: 100,
          autoScroll: true,
        }
      );

      // Connect data manager events to UI manager
      this.setupDataManagerEvents();

      // Replace the chat messages container with the enhanced UI
      this.replaceUIContainer();

      console.log("✅ Enhanced transcription managers initialized");
    } catch (error) {
      console.error("❌ Failed to initialize transcription managers:", error);
      // Fall back to basic functionality
      console.log("⚠️ Falling back to basic transcription handling");
    }
  }

  /**
   * Setup event connections between data manager and UI manager
   */
  setupDataManagerEvents() {
    if (!this.dataManager || !this.uiManager) return;

    // Connect data manager events to UI manager
    this.dataManager.on("interimResult", (data) => {
      this.uiManager.displayInterimResult(data);
    });

    this.dataManager.on("finalResult", (data) => {
      this.uiManager.displayFinalResult(data);
    });

    this.dataManager.on("sessionStart", (data) => {
      this.uiManager.handleSessionStart(data);
    });

    this.dataManager.on("sessionEnd", (data) => {
      this.uiManager.handleSessionEnd(data);
    });

    this.dataManager.on("speakerChange", (data) => {
      this.uiManager.handleSpeakerChange(data);
    });

    this.dataManager.on("interimExpired", (data) => {
      this.uiManager.handleInterimExpired(data);
    });

    this.dataManager.on("error", (data) => {
      console.error("❌ TranscriptionDataManager error:", data.error);
    });

    console.log("🔗 Data manager events connected to UI manager");
  }

  /**
   * Replace the basic chat UI with enhanced UI
   */
  replaceUIContainer() {
    if (!this.uiManager) return;

    // Hide the original chat messages container
    if (this.chatMessages) {
      this.chatMessages.style.display = "none";
    }

    console.log("🎨 Enhanced UI container replaced basic chat UI");
  }

  bindEvents() {
    // 绑定优化后的按钮事件
    this.primaryActionBtn.addEventListener("click", () =>
      this.handlePrimaryAction()
    );
  }

  // 处理主要操作按钮点击
  async handlePrimaryAction() {
    const currentState = this.primaryActionBtn.getAttribute("data-state");

    try {
      switch (currentState) {
        case "ready":
        case "error":
          // 直接开始完整流程：连接 + 转录
          await this.startFullTranscriptionFlow();
          break;
        case "recording":
          this.stopTranscription();
          break;
        default:
          console.log("按钮当前不可操作，状态:", currentState);
      }
    } catch (error) {
      console.error("操作失败:", error);
      this.updateButtonState("error");
    }
  }

  // 完整转录流程：连接 + 开始转录
  async startFullTranscriptionFlow() {
    try {
      // 步骤1: 连接
      await this.connect();

      // 步骤2: 连接成功后自动开始转录
      // 注意：onConnected 会被自动调用，我们在那里启动转录
    } catch (error) {
      console.error("完整转录流程失败:", error);
      this.updateButtonState("error");
      throw error;
    }
  }

  // 更新按钮状态
  updateButtonState(state) {
    const btnIcon = this.primaryActionBtn.querySelector(".btn-icon");
    const btnText = this.primaryActionBtn.querySelector(".btn-text");

    // 更新按钮状态属性
    this.primaryActionBtn.setAttribute("data-state", state);

    // 更新状态文本样式
    this.statusText.className = `status-text ${state}`;

    switch (state) {
      case "ready":
        btnIcon.textContent = "🎤";
        btnText.textContent = "开始转录";
        this.statusText.textContent = "就绪";
        this.primaryActionBtn.disabled = false;
        break;

      case "connecting":
        btnIcon.textContent = "⏳";
        btnText.textContent = "启动中";
        this.statusText.textContent = "启动中";
        this.primaryActionBtn.disabled = true;
        break;

      case "recording":
        btnIcon.textContent = "⏹️";
        btnText.textContent = "停止";
        this.statusText.textContent = "录音中 00:00";
        this.primaryActionBtn.disabled = false;
        break;

      case "error":
        btnIcon.textContent = "❌";
        btnText.textContent = "重试";
        this.statusText.textContent = "错误";
        this.primaryActionBtn.disabled = false;
        // 3秒后自动恢复
        setTimeout(() => this.updateButtonState("ready"), 3000);
        break;
    }
  }

  async connect() {
    try {
      console.log("🔄 开始连接流程...");
      this.updateButtonState("connecting");

      // 检查 LiveKit 客户端是否可用
      if (typeof LivekitClient === "undefined") {
        const error = {
          type: "CONFIG_MISSING",
          message: "LiveKit 客户端库未加载",
          userMessage: "LiveKit 客户端库未加载，请刷新页面重试",
          severity: "high",
          suggestions: ["刷新页面重新加载", "检查网络连接", "清除浏览器缓存"],
        };
        window.errorDisplayManager?.showError(error);
        throw new Error("LiveKit 客户端库未加载");
      }
      console.log("✅ LiveKit 客户端库已加载");

      // 获取访问令牌
      console.log("🔑 正在获取访问令牌...");
      const response = await fetch("/api/token", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          roomName: "transcription-room",
          participantName: "user-" + Math.random().toString(36).substr(2, 9),
        }),
      });

      if (!response.ok) {
        const error = {
          type: "AUTH_FAILED",
          message: `获取访问令牌失败: ${response.status} ${response.statusText}`,
          userMessage: "无法获取访问令牌，请稍后重试",
          severity: "high",
          retryable: true,
          suggestions: ["检查网络连接", "确认服务器运行正常", "稍后重试"],
        };
        window.errorDisplayManager?.showError(
          error,
          {},
          {
            retryFunction: () => this.connect(),
          }
        );
        throw new Error(
          `获取访问令牌失败: ${response.status} ${response.statusText}`
        );
      }
      console.log("✅ 访问令牌获取成功");

      const { token, url } = await response.json();

      console.log("获取到的连接信息:", {
        token: token ? "已获取" : "未获取",
        url,
      });

      // 连接到房间
      this.room = new LivekitClient.Room();

      // 监听房间事件
      this.room.on(LivekitClient.RoomEvent.Connected, () => {
        this.onConnected();
      });

      this.room.on(LivekitClient.RoomEvent.Disconnected, (reason) => {
        this.onDisconnected(reason);
      });

      this.room.on(
        LivekitClient.RoomEvent.ConnectionQualityChanged,
        (quality, participant) => {
          if (quality === "poor") {
            const warning = {
              type: "CONNECTION_QUALITY_POOR",
              message: "连接质量较差",
              userMessage: "网络连接质量较差，可能影响转录效果",
              severity: "medium",
              suggestions: [
                "检查网络连接稳定性",
                "尝试切换到更稳定的网络",
                "关闭其他占用带宽的应用",
              ],
            };
            window.errorDisplayManager?.showError(warning);
          }
        }
      );

      this.room.on(LivekitClient.RoomEvent.Reconnecting, () => {
        const info = {
          type: "CONNECTION_RECONNECTING",
          message: "正在重新连接...",
          userMessage: "连接中断，正在自动重新连接",
          severity: "medium",
          suggestions: ["请稍等，系统正在自动重连"],
        };
        window.errorDisplayManager?.showError(info);
      });

      this.room.on(LivekitClient.RoomEvent.Reconnected, () => {
        window.errorDisplayManager?.showSuccess("重新连接成功");
      });

      this.room.on(
        LivekitClient.RoomEvent.DataReceived,
        (payload, participant) => {
          this.handleDataReceived(payload, participant);
        }
      );

      this.room.on(
        LivekitClient.RoomEvent.ParticipantConnected,
        (participant) => {
          console.log("参与者已连接:", participant.identity);
        }
      );

      // 连接到房间 - 使用服务器返回的正确 URL
      console.log("正在连接到:", url);
      await this.room.connect(url, token);
    } catch (error) {
      console.error("连接失败:", error);
      this.updateButtonState("error");

      // Show user-friendly error if not already shown
      if (!error.userErrorShown) {
        const connectionError = {
          type: "CONNECTION_FAILED",
          message: error.message,
          userMessage: "连接失败，请检查网络后重试",
          severity: "high",
          retryable: true,
          suggestions: [
            "检查网络连接",
            "确认服务器运行正常",
            "尝试刷新页面",
            "稍后重试",
          ],
        };
        window.errorDisplayManager?.showError(
          connectionError,
          {},
          {
            retryFunction: () => this.connect(),
          }
        );
      }

      throw error;
    }
  }

  async onConnected() {
    console.log("已连接到房间");

    this.sessionStartTime = Date.now();
    this.startSessionTimer();

    // Notify data manager of session start
    if (this.dataManager) {
      this.dataManager.processTranscriptionData({
        type: "session_start",
        participant: "current-user",
        timestamp: Date.now(),
      });
    }

    // 请求麦克风权限并发布音频
    try {
      await this.publishMicrophone();

      // 连接成功后等待 Agent 转录
      console.log("🤖 等待 Node.js Agent 处理转录...");
      console.log("📡 Agent 将自动处理音频并发送转录结果");

      // 更新按钮状态为录音中
      this.updateButtonState("recording");
    } catch (error) {
      console.error("发布麦克风或启动转录失败:", error);
      this.updateButtonState("error");
    }
  }

  async publishMicrophone() {
    console.log("正在发布麦克风...");

    try {
      // 尝试不同的 API 方式
      console.log("尝试发布音频轨道...");

      if (LivekitClient.createLocalAudioTrack) {
        // 方式 1: 使用 createLocalAudioTrack 让它自己处理媒体获取
        console.log("使用 createLocalAudioTrack 方法");
        const localTrack = await LivekitClient.createLocalAudioTrack({
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        });
        await this.room.localParticipant.publishTrack(localTrack);
        console.log("✅ 麦克风已发布 (使用 createLocalAudioTrack)");
      } else {
        // 方式 2: 手动获取媒体流并发布
        console.log("手动获取媒体流");
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          },
        });

        const audioTrack = stream.getAudioTracks()[0];
        await this.room.localParticipant.publishTrack(audioTrack, {
          name: "microphone",
          source: "microphone",
        });
        console.log("✅ 麦克风已发布 (手动方式)");
      }
    } catch (error) {
      console.error("❌ 发布麦克风失败:", error);

      // Check if it's a permission error
      if (
        error.name === "NotAllowedError" ||
        error.message.includes("Permission denied")
      ) {
        const permissionError = {
          type: "MICROPHONE_ACCESS_DENIED",
          message: "麦克风权限被拒绝",
          userMessage: "需要麦克风权限才能进行语音转录",
          severity: "high",
          retryable: true,
          suggestions: [
            "点击浏览器地址栏的麦克风图标",
            '选择"允许"麦克风权限',
            "刷新页面重新尝试",
            "检查系统麦克风设置",
          ],
        };
        window.errorDisplayManager?.showError(
          permissionError,
          {},
          {
            retryFunction: () => this.publishMicrophone(),
          }
        );
        throw error;
      }

      // Try basic media permission check for more detailed error info
      try {
        console.log("🔍 检查媒体权限...");
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
        console.log("✅ 媒体权限正常，但 LiveKit 发布失败");
        stream.getTracks().forEach((track) => track.stop());

        // If permissions are OK but LiveKit failed, it's a different issue
        const publishError = {
          type: "AUDIO_TRACK_FAILED",
          message: "音频轨道发布失败",
          userMessage: "麦克风权限正常，但音频发布失败",
          severity: "high",
          retryable: true,
          suggestions: [
            "检查音频设备是否正常工作",
            "尝试刷新页面重新连接",
            "检查浏览器兼容性",
            "尝试使用不同的浏览器",
          ],
        };
        window.errorDisplayManager?.showError(
          publishError,
          {},
          {
            retryFunction: () => this.publishMicrophone(),
          }
        );
      } catch (mediaError) {
        console.error("❌ 媒体权限失败:", mediaError);

        const mediaPermissionError = {
          type: "MICROPHONE_ACCESS_DENIED",
          message: "无法访问麦克风设备",
          userMessage: "无法访问麦克风，请检查设备和权限设置",
          severity: "critical",
          retryable: true,
          suggestions: [
            "确认麦克风设备已连接",
            "检查系统音频设备设置",
            "授予浏览器麦克风权限",
            "重启浏览器后重试",
          ],
        };
        window.errorDisplayManager?.showError(
          mediaPermissionError,
          {},
          {
            retryFunction: () => this.publishMicrophone(),
          }
        );
      }

      throw error;
    }
  }

  onDisconnected() {
    console.log("已断开连接");

    // Notify data manager of session end
    if (this.dataManager) {
      this.dataManager.processTranscriptionData({
        type: "session_end",
        participant: "current-user",
        timestamp: Date.now(),
      });
    }

    this.updateButtonState("ready");
    this.stopSessionTimer();
  }

  async disconnect() {
    if (this.room) {
      await this.room.disconnect();
      this.room = null;
    }
  }

  handleDataReceived(payload, participant) {
    try {
      const data = JSON.parse(new TextDecoder().decode(payload));
      console.log("收到数据:", data);

      // Use enhanced data manager if available
      if (this.dataManager) {
        const processed = this.dataManager.processTranscriptionData(data);
        if (processed) {
          console.log("✅ Data processed by enhanced manager");
          return;
        }
      }

      // Fallback to basic handling
      switch (data.type) {
        case "transcription":
          // Handle generic transcription message
          if (data.isFinal === false) {
            this.updateInterimMessage(data.text, data.speaker);
          } else {
            this.addFinalTranscription(data);
            this.updateCurrentTranscription("等待语音输入...", false);
          }
          break;

        case "interim_transcript":
        case "interim":
          this.updateInterimMessage(data.text, data.speaker);
          break;

        case "final_transcript":
        case "final":
          this.addFinalTranscription(data);
          this.updateCurrentTranscription("等待语音输入...", false);
          break;

        case "status":
          console.log("状态更新:", data.message);
          break;

        case "session_summary":
          console.log("会话总结:", data);
          break;

        default:
          console.log("未知数据类型:", data.type);
      }
    } catch (error) {
      console.error("处理数据时出错:", error);
    }
  }

  // 更新临时消息（正在识别语音）- Enhanced version
  updateInterimMessage(text, speakerData) {
    console.log("🔄 updateInterimMessage:", text, "说话人:", speakerData);

    // Use enhanced data manager if available
    if (this.dataManager) {
      const interimData = {
        type: "interim",
        text: text,
        speaker: speakerData,
        timestamp: Date.now(),
        participant: "current-user",
        isFinal: false,
      };

      this.dataManager.processTranscriptionData(interimData);
      return;
    }

    // Fallback to basic handling
    if (!this.chatMessages) {
      console.error("❌ chatMessages 元素不存在");
      return;
    }

    // 移除欢迎消息（如果存在）
    const welcomeMessage = this.chatMessages.querySelector(".welcome-message");
    if (welcomeMessage) {
      console.log("🗑️ 移除欢迎消息");
      welcomeMessage.remove();
    }

    // 查找当前的临时消息
    let interimMessage = this.chatMessages.querySelector(".interim-message");

    if (!interimMessage) {
      // 在创建新临时消息前，先保存之前可能存在的临时消息
      this.saveAnyExistingInterimMessage();

      // 创建新的临时消息
      console.log("➕ 创建新的临时消息");
      const speakerInfo = this.getSpeakerInfo(speakerData, true); // 传递 isInterim=true
      console.log("👤 临时消息说话人信息:", speakerInfo);

      interimMessage = document.createElement("div");
      interimMessage.className = "transcription-message interim-message";

      interimMessage.innerHTML = `
        <div class="message-bubble interim-bubble ${speakerInfo.class}">
          <div class="message-header">
            <div class="speaker-info">
              <span class="speaker-icon">${speakerInfo.icon}</span>
              <span class="speaker-name">${speakerInfo.name}</span>
            </div>
            <div class="message-meta-inline">
              <span class="interim-status">正在识别...</span>
            </div>
          </div>
          <div class="message-content interim-content">
            <div class="interim-row">
              <div class="wave-animation">
                <span class="wave-bar"></span>
                <span class="wave-bar"></span>
                <span class="wave-bar"></span>
              </div>
              <span class="interim-text">${text}</span>
            </div>
          </div>
        </div>
      `;

      this.chatMessages.appendChild(interimMessage);
    } else {
      // 更新现有的临时消息
      console.log("🔄 更新现有临时消息");
      const interimTextSpan = interimMessage.querySelector(".interim-text");
      if (interimTextSpan) {
        console.log("📝 更新前文本:", interimTextSpan.textContent);
        interimTextSpan.textContent = text;
        console.log("📝 更新后文本:", interimTextSpan.textContent);

        // 强制DOM刷新
        interimTextSpan.style.display = "none";
        interimTextSpan.offsetHeight; // 触发重排
        interimTextSpan.style.display = "";

        console.log(
          "📍 DOM元素可见性:",
          window.getComputedStyle(interimTextSpan).display
        );
      } else {
        console.error("❌ 找不到 .interim-text 元素");
        console.log("🔍 临时消息结构:", interimMessage.innerHTML);
      }

      // 确保音波动画继续运行
      const waveAnimation = interimMessage.querySelector(".wave-animation");
      if (waveAnimation) {
        waveAnimation.querySelectorAll(".wave-bar").forEach((bar, index) => {
          bar.style.animation = `wave-bounce 1.2s ease-in-out infinite`;
          bar.style.animationDelay = `${index * 0.2}s`;
        });
      }
    }

    // 检查消息容器可见性
    console.log("📍 临时消息可见性:", {
      display: window.getComputedStyle(interimMessage).display,
      visibility: window.getComputedStyle(interimMessage).visibility,
      opacity: window.getComputedStyle(interimMessage).opacity,
      height: interimMessage.offsetHeight,
      width: interimMessage.offsetWidth,
    });

    // 平滑滚动到底部
    this.smoothScrollToBottom();
  }

  // 保留原方法用于隐藏底部指示器
  updateCurrentTranscription(text, isInterim) {
    // 现在只用于隐藏底部指示器
    if (!isInterim && this.currentTranscription) {
      this.currentTranscription.style.display = "none";
    }
  }

  addFinalTranscription(data) {
    console.log("🎯 addFinalTranscription 被调用:", data);

    this.transcriptions.push(data);

    // Use enhanced data manager if available
    if (this.dataManager) {
      const finalData = {
        ...data,
        type: "final",
        participant: data.participant || "current-user",
        isFinal: true,
      };

      this.dataManager.processTranscriptionData(finalData);
      return;
    }

    // Fallback to basic handling
    if (!this.chatMessages) {
      console.error("❌ chatMessages 元素不存在");
      return;
    }

    // 查找并移除当前的临时消息
    const interimMessage = this.chatMessages.querySelector(".interim-message");
    if (interimMessage) {
      console.log("🔄 将临时消息转换为最终消息");
      interimMessage.remove();
    }

    // 创建最终转录消息
    const messageDiv = document.createElement("div");
    messageDiv.className = "transcription-message final-message";

    const confidenceClass = this.getConfidenceClass(data.confidence);
    const timestamp = new Date(data.timestamp).toLocaleTimeString();

    // 获取说话人信息
    console.log("🎤 转录数据中的说话人信息:", data.speaker);
    const speakerInfo = this.getSpeakerInfo(data.speaker, false); // 传递 isInterim=false
    console.log("👤 解析后的说话人信息:", speakerInfo);

    messageDiv.innerHTML = `
      <div class="message-bubble ${speakerInfo.class}">
        <div class="message-header">
          <div class="speaker-info">
            <span class="speaker-icon">${speakerInfo.icon}</span>
            <span class="speaker-name">${speakerInfo.name}</span>
          </div>
          <div class="message-meta-inline">
            <span class="confidence-badge ${confidenceClass}">
              ${Math.round(data.confidence * 100)}%
            </span>
            <span class="timestamp">${timestamp}</span>
          </div>
        </div>
        <div class="message-content">${data.text}</div>
      </div>
    `;

    // 添加到聊天消息区域
    this.chatMessages.appendChild(messageDiv);
    console.log("✅ 最终转录消息已添加到 ChatUI");

    // 平滑滚动到底部
    this.smoothScrollToBottom();
  }

  // 获取说话人信息
  getSpeakerInfo(speakerData, isInterim = false) {
    if (!speakerData || speakerData.speaker === undefined) {
      return {
        name: isInterim ? "发言者" : "发言者",
        icon: "👤",
        class: "speaker-default",
      };
    }

    const speakerNumber = speakerData.speaker;

    // 如果是临时消息，只显示"发言者"
    if (isInterim) {
      return {
        name: "发言者",
        icon: "👤",
        class: "speaker-default",
      };
    }

    // 最终消息显示具体的发言者 A、B、C...
    const speakerColors = [
      { name: "发言者 A", icon: "👤", class: "speaker-0" },
      { name: "发言者 B", icon: "👥", class: "speaker-1" },
      { name: "发言者 C", icon: "🗣️", class: "speaker-2" },
      { name: "发言者 D", icon: "💬", class: "speaker-3" },
      { name: "发言者 E", icon: "🎤", class: "speaker-4" },
    ];

    return (
      speakerColors[speakerNumber] || {
        name: `发言者 ${String.fromCharCode(65 + speakerNumber)}`,
        icon: "👤",
        class: `speaker-${speakerNumber % 5}`,
      }
    );
  }

  getConfidenceClass(confidence) {
    if (confidence >= 0.8) return "high";
    if (confidence >= 0.6) return "medium";
    return "low";
  }

  startSessionTimer() {
    this.sessionTimer = setInterval(() => {
      if (this.sessionStartTime) {
        const elapsed = Date.now() - this.sessionStartTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);
        const timeStr = `${minutes.toString().padStart(2, "0")}:${seconds
          .toString()
          .padStart(2, "0")}`;

        // 更新状态文本中的时长显示
        if (this.primaryActionBtn.getAttribute("data-state") === "recording") {
          this.statusText.textContent = `录音中 ${timeStr}`;
        }
      }
    }, 1000);
  }

  stopSessionTimer() {
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer);
      this.sessionTimer = null;
    }
  }

  stopTranscription() {
    try {
      console.log("🛑 停止转录...");

      // 检查是否有未完成的临时消息，将其转为正式消息
      this.convertInterimToFinal();

      // 断开 LiveKit 连接
      this.disconnect();

      this.updateButtonState("ready");
    } catch (error) {
      console.error("停止转录失败:", error);
      this.updateButtonState("error");
    }
  }

  // 平滑滚动到底部
  smoothScrollToBottom() {
    if (!this.chatMessages) return;

    this.chatMessages.scrollTo({
      top: this.chatMessages.scrollHeight,
      behavior: "smooth",
    });
  }

  // 保存任何现有的临时消息
  saveAnyExistingInterimMessage() {
    if (!this.chatMessages) return;

    const existingInterim = this.chatMessages.querySelector(".interim-message");
    if (existingInterim) {
      const interimText = existingInterim.querySelector(".interim-text");
      if (interimText && interimText.textContent.trim()) {
        console.log("💾 保存现有临时消息:", interimText.textContent.trim());

        // 创建最终消息数据
        const finalData = {
          text: interimText.textContent.trim(),
          confidence: 0.85, // 给一个合理的默认置信度
          timestamp: Date.now(),
          language: "multi",
          speaker: { speaker: 0, confidence: 0.8, source: "interim-save" },
        };

        // 移除临时消息
        existingInterim.remove();

        // 添加为最终消息
        this.addFinalTranscription(finalData);
      }
    }
  }

  // 将临时消息转换为正式消息
  convertInterimToFinal() {
    if (!this.chatMessages) return;

    const interimMessage = this.chatMessages.querySelector(".interim-message");
    if (interimMessage) {
      const interimText = interimMessage.querySelector(".interim-text");
      if (interimText && interimText.textContent.trim()) {
        console.log(
          "🔄 将未完成的临时消息转为正式消息:",
          interimText.textContent
        );

        // 创建正式消息数据
        const finalData = {
          text: interimText.textContent.trim(),
          confidence: 0.8, // 给一个默认置信度
          timestamp: Date.now(),
          language: "zh",
        };

        // 移除临时消息
        interimMessage.remove();

        // 添加为正式消息
        this.addFinalTranscription(finalData);
      }
    }
  }

  // 显示转录结果
  displayTranscription(transcription) {
    const transcriptionHistoryDiv = document.getElementById(
      "transcriptionHistory"
    );
    if (!transcriptionHistoryDiv) return;

    const transcriptionItem = document.createElement("div");
    transcriptionItem.className = "transcription-item";
    transcriptionItem.innerHTML = `
      <div class="transcription-text">${transcription.text}</div>
      <div class="transcription-meta">
        置信度: ${(transcription.confidence * 100).toFixed(1)}% |
        时间: ${new Date(transcription.timestamp).toLocaleTimeString()}
      </div>
    `;

    transcriptionHistoryDiv.appendChild(transcriptionItem);
    transcriptionHistoryDiv.scrollTop = transcriptionHistoryDiv.scrollHeight;

    console.log(
      `📝 转录结果: ${transcription.text} (置信度: ${(
        transcription.confidence * 100
      ).toFixed(1)}%)`
    );
  }
}

// 初始化应用
document.addEventListener("DOMContentLoaded", () => {
  const app = new TranscriptionApp();
  window.transcriptionApp = app;

  // 初始化模型控制
  initializeModelControls(app);
});

// 初始化模型控制
async function initializeModelControls(app) {
  const configStatusSpan = document.getElementById("configStatus");

  // 简化配置状态显示
  configStatusSpan.textContent = "配置: LiveKit + Deepgram Agent";
  console.log("🔧 LiveKit Agent 模式已启用");
}
