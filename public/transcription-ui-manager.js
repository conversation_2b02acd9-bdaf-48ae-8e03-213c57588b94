/**
 * TranscriptionUIManager - Enhanced UI management for transcription display
 * Handles visual indicators, speaker diarization display, and multi-participant UI
 */

class TranscriptionUIManager {
  constructor(containerElement, options = {}) {
    this.container = containerElement;
    this.options = {
      enableAnimations: true,
      showConfidence: true,
      showTimestamps: true,
      showSpeakerInfo: true,
      maxVisibleMessages: 100,
      autoScroll: true,
      compactMode: false,
      ...options,
    };

    // UI elements
    this.messagesContainer = null;
    this.participantsList = null;
    this.speakersList = null;
    this.statsPanel = null;

    // State
    this.visibleMessages = [];
    this.activeInterimMessages = new Map();
    this.participantColors = new Map();
    this.speakerElements = new Map();

    // Animation queues
    this.animationQueue = [];
    this.isAnimating = false;

    this.initializeUI();
    console.log("🎨 TranscriptionUIManager initialized");
  }

  /**
   * Initialize the UI structure
   */
  initializeUI() {
    if (!this.container) {
      console.error("❌ Container element not provided");
      return;
    }

    // Create main UI structure
    this.container.innerHTML = `
      <div class="transcription-ui-container">
        <div class="transcription-header">
          <div class="session-info">
            <h3 class="session-title">💬 Live Transcription</h3>
            <div class="session-stats">
              <span class="stat-item" id="participantCount">👥 0</span>
              <span class="stat-item" id="speakerCount">🗣️ 0</span>
              <span class="stat-item" id="messageCount">📝 0</span>
            </div>
          </div>
          <div class="ui-controls">
            <button class="ui-btn" id="toggleCompact" title="Toggle compact mode">
              📋
            </button>
            <button class="ui-btn" id="clearMessages" title="Clear messages">
              🗑️
            </button>
            <button class="ui-btn" id="exportData" title="Export data">
              💾
            </button>
          </div>
        </div>
        
        <div class="transcription-body">
          <div class="participants-sidebar" id="participantsSidebar">
            <div class="sidebar-header">
              <h4>👥 Participants</h4>
            </div>
            <div class="participants-list" id="participantsList"></div>
            
            <div class="sidebar-header">
              <h4>🗣️ Speakers</h4>
            </div>
            <div class="speakers-list" id="speakersList"></div>
          </div>
          
          <div class="messages-area">
            <div class="messages-container" id="messagesContainer">
              <div class="welcome-message">
                <div class="system-message">
                  <span class="message-icon">🎤</span>
                  <span class="message-text">Ready for transcription...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    // Get references to UI elements
    this.messagesContainer = this.container.querySelector("#messagesContainer");
    this.participantsList = this.container.querySelector("#participantsList");
    this.speakersList = this.container.querySelector("#speakersList");

    // Bind event listeners
    this.bindUIEvents();

    // Apply initial styles
    this.applyStyles();
  }

  /**
   * Apply CSS styles
   */
  applyStyles() {
    const styles = `
      <style id="transcription-ui-styles">
        .transcription-ui-container {
          display: flex;
          flex-direction: column;
          height: 100%;
          background: #fff;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .transcription-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px;
          background: #f8f9fa;
          border-bottom: 1px solid #e9ecef;
        }

        .session-info {
          display: flex;
          align-items: center;
          gap: 20px;
        }

        .session-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
        }

        .session-stats {
          display: flex;
          gap: 15px;
        }

        .stat-item {
          font-size: 14px;
          color: #6c757d;
          font-weight: 500;
        }

        .ui-controls {
          display: flex;
          gap: 8px;
        }

        .ui-btn {
          padding: 8px 12px;
          border: 1px solid #dee2e6;
          background: #fff;
          border-radius: 6px;
          cursor: pointer;
          font-size: 16px;
          transition: all 0.2s ease;
        }

        .ui-btn:hover {
          background: #e9ecef;
          border-color: #adb5bd;
        }

        .transcription-body {
          display: flex;
          flex: 1;
          overflow: hidden;
        }

        .participants-sidebar {
          width: 250px;
          background: #f8f9fa;
          border-right: 1px solid #e9ecef;
          overflow-y: auto;
          padding: 16px;
        }

        .sidebar-header {
          margin-bottom: 12px;
        }

        .sidebar-header h4 {
          margin: 0;
          font-size: 14px;
          font-weight: 600;
          color: #495057;
        }

        .participants-list, .speakers-list {
          margin-bottom: 24px;
        }

        .participant-item, .speaker-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          margin-bottom: 4px;
          background: #fff;
          border-radius: 6px;
          border-left: 3px solid #dee2e6;
          font-size: 13px;
          transition: all 0.2s ease;
        }

        .participant-item.active, .speaker-item.active {
          border-left-color: #007bff;
          background: #e3f2fd;
        }

        .participant-icon, .speaker-icon {
          font-size: 14px;
        }

        .participant-info, .speaker-info {
          flex: 1;
        }

        .participant-name, .speaker-name {
          font-weight: 500;
          color: #2c3e50;
        }

        .participant-stats, .speaker-stats {
          font-size: 11px;
          color: #6c757d;
        }

        .messages-area {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        .messages-container {
          flex: 1;
          overflow-y: auto;
          padding: 20px;
          scroll-behavior: smooth;
        }

        .welcome-message {
          text-align: center;
          margin: 40px 0;
        }

        .system-message {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          background: #e3f2fd;
          color: #1976d2;
          padding: 12px 16px;
          border-radius: 20px;
          font-size: 14px;
          font-weight: 500;
        }

        .message-icon {
          font-size: 16px;
        }

        /* Message styles */
        .transcription-message {
          margin-bottom: 16px;
          animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .message-bubble {
          background: #fff;
          border: 1px solid #e1e5e9;
          border-radius: 12px;
          padding: 12px 16px;
          max-width: 85%;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          position: relative;
        }

        .interim-message .message-bubble {
          background: #f8f9fa;
          border: 1px dashed #6c757d;
          box-shadow: none;
        }

        .message-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px;
          font-size: 12px;
        }

        .speaker-info {
          display: flex;
          align-items: center;
          gap: 6px;
          font-weight: 600;
          color: #495057;
        }

        .speaker-icon {
          font-size: 14px;
        }

        .message-meta-inline {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 11px;
          color: #6c757d;
        }

        .confidence-badge {
          padding: 2px 6px;
          border-radius: 10px;
          font-size: 11px;
          font-weight: 500;
        }

        .confidence-badge.high {
          background: #d4edda;
          color: #155724;
        }

        .confidence-badge.medium {
          background: #fff3cd;
          color: #856404;
        }

        .confidence-badge.low {
          background: #f8d7da;
          color: #721c24;
        }

        .message-content {
          font-size: 14px;
          line-height: 1.5;
          color: #2c3e50;
        }

        .interim-content {
          display: flex;
          align-items: center;
          font-style: italic;
          color: #6c757d;
        }

        .interim-row {
          display: flex;
          align-items: center;
          gap: 8px;
          width: 100%;
        }

        .wave-animation {
          display: flex;
          align-items: center;
          gap: 2px;
          height: 16px;
        }

        .wave-bar {
          width: 3px;
          background: #007bff;
          border-radius: 2px;
          animation: wave-bounce 1.2s ease-in-out infinite;
        }

        .wave-bar:nth-child(1) {
          height: 8px;
          animation-delay: 0s;
        }

        .wave-bar:nth-child(2) {
          height: 12px;
          animation-delay: 0.2s;
        }

        .wave-bar:nth-child(3) {
          height: 6px;
          animation-delay: 0.4s;
        }

        @keyframes wave-bounce {
          0%, 100% {
            transform: scaleY(0.5);
            opacity: 0.7;
          }
          50% {
            transform: scaleY(1);
            opacity: 1;
          }
        }

        .interim-text {
          font-size: 14px;
          flex: 1;
          color: #6c757d;
          font-style: italic;
        }

        /* Speaker color classes */
        .speaker-0 { border-left: 4px solid #007bff; }
        .speaker-1 { border-left: 4px solid #28a745; }
        .speaker-2 { border-left: 4px solid #ffc107; }
        .speaker-3 { border-left: 4px solid #dc3545; }
        .speaker-4 { border-left: 4px solid #6f42c1; }
        .speaker-5 { border-left: 4px solid #fd7e14; }
        .speaker-6 { border-left: 4px solid #20c997; }
        .speaker-7 { border-left: 4px solid #e83e8c; }
        .speaker-default { border-left: 4px solid #6c757d; }

        /* Compact mode */
        .transcription-ui-container.compact .message-bubble {
          padding: 8px 12px;
        }

        .transcription-ui-container.compact .message-header {
          font-size: 11px;
        }

        .transcription-ui-container.compact .message-content {
          font-size: 13px;
        }

        .transcription-ui-container.compact .participants-sidebar {
          width: 200px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
          .transcription-body {
            flex-direction: column;
          }

          .participants-sidebar {
            width: 100%;
            max-height: 200px;
            border-right: none;
            border-bottom: 1px solid #e9ecef;
          }

          .session-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }

          .session-stats {
            gap: 10px;
          }
        }
      </style>
    `;

    // Add styles to document head
    const existingStyles = document.getElementById("transcription-ui-styles");
    if (existingStyles) {
      existingStyles.remove();
    }
    document.head.insertAdjacentHTML("beforeend", styles);
  }

  /**
   * Bind UI event listeners
   */
  bindUIEvents() {
    // Toggle compact mode
    const toggleCompactBtn = this.container.querySelector("#toggleCompact");
    if (toggleCompactBtn) {
      toggleCompactBtn.addEventListener("click", () => {
        this.toggleCompactMode();
      });
    }

    // Clear messages
    const clearBtn = this.container.querySelector("#clearMessages");
    if (clearBtn) {
      clearBtn.addEventListener("click", () => {
        this.clearMessages();
      });
    }

    // Export data
    const exportBtn = this.container.querySelector("#exportData");
    if (exportBtn) {
      exportBtn.addEventListener("click", () => {
        this.exportData();
      });
    }
  }

  /**
   * Display interim transcription result
   */
  displayInterimResult(data) {
    console.log("🔄 Displaying interim result:", data);

    const { trackId, data: transcriptionData, participantInfo } = data;

    // Remove welcome message if present
    this.removeWelcomeMessage();

    // Check if we already have an interim message for this track
    let interimElement = this.activeInterimMessages.get(trackId);

    if (!interimElement) {
      // Create new interim message
      interimElement = this.createInterimMessage(
        transcriptionData,
        participantInfo
      );
      this.messagesContainer.appendChild(interimElement);
      this.activeInterimMessages.set(trackId, interimElement);
    } else {
      // Update existing interim message
      this.updateInterimMessage(interimElement, transcriptionData);
    }

    // Update UI state
    this.updateParticipantActivity(participantInfo);
    this.updateSpeakerActivity(transcriptionData.speaker);

    // Auto-scroll if enabled
    if (this.options.autoScroll) {
      this.scrollToBottom();
    }
  }

  /**
   * Display final transcription result
   */
  displayFinalResult(data) {
    console.log("✅ Displaying final result:", data);

    const {
      trackId,
      data: transcriptionData,
      participantInfo,
      interimData,
    } = data;

    // Remove corresponding interim message
    const interimElement = this.activeInterimMessages.get(trackId);
    if (interimElement) {
      this.removeInterimMessage(trackId);
    }

    // Create final message
    const finalElement = this.createFinalMessage(
      transcriptionData,
      participantInfo
    );
    this.messagesContainer.appendChild(finalElement);
    this.visibleMessages.push(finalElement);

    // Maintain message limit
    this.maintainMessageLimit();

    // Update UI state
    this.updateParticipantActivity(participantInfo);
    this.updateSpeakerActivity(transcriptionData.speaker);
    this.updateStats();

    // Auto-scroll if enabled
    if (this.options.autoScroll) {
      this.scrollToBottom();
    }
  }

  /**
   * Create interim message element
   */
  createInterimMessage(data, participantInfo) {
    const messageDiv = document.createElement("div");
    messageDiv.className = "transcription-message interim-message";
    messageDiv.dataset.trackId = data.trackId;

    const speakerClass = this.getSpeakerClass(data.speaker);
    const speakerInfo = this.getSpeakerDisplayInfo(data.speaker);

    messageDiv.innerHTML = `
      <div class="message-bubble ${speakerClass}">
        ${
          this.options.showSpeakerInfo
            ? `
          <div class="message-header">
            <div class="speaker-info">
              <span class="speaker-icon">${speakerInfo.icon}</span>
              <span class="speaker-name">${speakerInfo.name}</span>
            </div>
            <div class="message-meta-inline">
              <span class="interim-status">Recognizing...</span>
            </div>
          </div>
        `
            : ""
        }
        <div class="message-content interim-content">
          <div class="interim-row">
            <div class="wave-animation">
              <span class="wave-bar"></span>
              <span class="wave-bar"></span>
              <span class="wave-bar"></span>
            </div>
            <span class="interim-text">${data.text}</span>
          </div>
        </div>
      </div>
    `;

    return messageDiv;
  }

  /**
   * Update interim message content
   */
  updateInterimMessage(element, data) {
    const interimText = element.querySelector(".interim-text");
    if (interimText) {
      interimText.textContent = data.text;
    }
  }

  /**
   * Create final message element
   */
  createFinalMessage(data, participantInfo) {
    const messageDiv = document.createElement("div");
    messageDiv.className = "transcription-message final-message";
    messageDiv.dataset.messageId = data.id;

    const speakerClass = this.getSpeakerClass(data.speaker);
    const speakerInfo = this.getSpeakerDisplayInfo(data.speaker);
    const confidenceClass = this.getConfidenceClass(data.confidence);
    const timestamp = new Date(data.timestamp).toLocaleTimeString();

    messageDiv.innerHTML = `
      <div class="message-bubble ${speakerClass}">
        ${
          this.options.showSpeakerInfo
            ? `
          <div class="message-header">
            <div class="speaker-info">
              <span class="speaker-icon">${speakerInfo.icon}</span>
              <span class="speaker-name">${speakerInfo.name}</span>
            </div>
            <div class="message-meta-inline">
              ${
                this.options.showConfidence
                  ? `
                <span class="confidence-badge ${confidenceClass}">
                  ${Math.round(data.confidence * 100)}%
                </span>
              `
                  : ""
              }
              ${
                this.options.showTimestamps
                  ? `
                <span class="timestamp">${timestamp}</span>
              `
                  : ""
              }
            </div>
          </div>
        `
            : ""
        }
        <div class="message-content">${data.text}</div>
      </div>
    `;

    return messageDiv;
  }

  /**
   * Remove interim message
   */
  removeInterimMessage(trackId) {
    const element = this.activeInterimMessages.get(trackId);
    if (element) {
      element.remove();
      this.activeInterimMessages.delete(trackId);
    }
  }

  /**
   * Remove welcome message
   */
  removeWelcomeMessage() {
    const welcomeMessage =
      this.messagesContainer.querySelector(".welcome-message");
    if (welcomeMessage) {
      welcomeMessage.remove();
    }
  }

  /**
   * Update participant activity in sidebar
   */
  updateParticipantActivity(participantInfo) {
    if (!participantInfo) return;

    let participantElement = this.participantsList.querySelector(
      `[data-participant-id="${participantInfo.id}"]`
    );

    if (!participantElement) {
      participantElement = this.createParticipantElement(participantInfo);
      this.participantsList.appendChild(participantElement);
    }

    // Update participant stats
    const statsElement = participantElement.querySelector(".participant-stats");
    if (statsElement) {
      statsElement.textContent = `${participantInfo.transcriptionCount} messages`;
    }

    // Add active class temporarily
    participantElement.classList.add("active");
    setTimeout(() => {
      participantElement.classList.remove("active");
    }, 2000);
  }

  /**
   * Update speaker activity in sidebar
   */
  updateSpeakerActivity(speakerData) {
    if (!speakerData || !this.options.showSpeakerInfo) return;

    const speakerId = speakerData.id;
    let speakerElement = this.speakersList.querySelector(
      `[data-speaker-id="${speakerId}"]`
    );

    if (!speakerElement) {
      speakerElement = this.createSpeakerElement(speakerData);
      this.speakersList.appendChild(speakerElement);
    }

    // Update speaker stats
    const statsElement = speakerElement.querySelector(".speaker-stats");
    if (statsElement && speakerData.profile) {
      statsElement.textContent = `${speakerData.profile.transcriptionCount} messages`;
    }

    // Add active class temporarily
    speakerElement.classList.add("active");
    setTimeout(() => {
      speakerElement.classList.remove("active");
    }, 2000);
  }

  /**
   * Create participant element for sidebar
   */
  createParticipantElement(participantInfo) {
    const element = document.createElement("div");
    element.className = "participant-item";
    element.dataset.participantId = participantInfo.id;

    element.innerHTML = `
      <span class="participant-icon">👤</span>
      <div class="participant-info">
        <div class="participant-name">${participantInfo.name}</div>
        <div class="participant-stats">${
          participantInfo.transcriptionCount || 0
        } messages</div>
      </div>
    `;

    return element;
  }

  /**
   * Create speaker element for sidebar
   */
  createSpeakerElement(speakerData) {
    const element = document.createElement("div");
    element.className = "speaker-item";
    element.dataset.speakerId = speakerData.id;

    const speakerInfo = this.getSpeakerDisplayInfo(speakerData);

    element.innerHTML = `
      <span class="speaker-icon" style="color: ${
        speakerData.profile?.color || "#6c757d"
      }">${speakerInfo.icon}</span>
      <div class="speaker-info">
        <div class="speaker-name">${speakerInfo.name}</div>
        <div class="speaker-stats">${
          speakerData.profile?.transcriptionCount || 0
        } messages</div>
      </div>
    `;

    return element;
  }

  /**
   * Get speaker CSS class
   */
  getSpeakerClass(speakerData) {
    if (!speakerData || speakerData.id === undefined) {
      return "speaker-default";
    }
    return `speaker-${speakerData.id % 8}`;
  }

  /**
   * Get speaker display information
   */
  getSpeakerDisplayInfo(speakerData) {
    if (!speakerData) {
      return { name: "Speaker", icon: "👤" };
    }

    return {
      name:
        speakerData.name ||
        `Speaker ${String.fromCharCode(65 + (speakerData.id % 26))}`,
      icon: speakerData.profile?.icon || this.getSpeakerIcon(speakerData.id),
    };
  }

  /**
   * Get speaker icon
   */
  getSpeakerIcon(speakerId) {
    const icons = ["👤", "👥", "🗣️", "💬", "🎤", "👨", "👩", "🧑"];
    return icons[speakerId % icons.length];
  }

  /**
   * Get confidence CSS class
   */
  getConfidenceClass(confidence) {
    if (confidence >= 0.8) return "high";
    if (confidence >= 0.6) return "medium";
    return "low";
  }

  /**
   * Update statistics display
   */
  updateStats() {
    const participantCount = this.container.querySelector("#participantCount");
    const speakerCount = this.container.querySelector("#speakerCount");
    const messageCount = this.container.querySelector("#messageCount");

    if (participantCount) {
      participantCount.textContent = `👥 ${this.participantsList.children.length}`;
    }

    if (speakerCount) {
      speakerCount.textContent = `🗣️ ${this.speakersList.children.length}`;
    }

    if (messageCount) {
      messageCount.textContent = `📝 ${this.visibleMessages.length}`;
    }
  }

  /**
   * Maintain message limit
   */
  maintainMessageLimit() {
    while (this.visibleMessages.length > this.options.maxVisibleMessages) {
      const oldestMessage = this.visibleMessages.shift();
      if (oldestMessage && oldestMessage.parentNode) {
        oldestMessage.remove();
      }
    }
  }

  /**
   * Scroll to bottom of messages
   */
  scrollToBottom() {
    if (this.messagesContainer) {
      this.messagesContainer.scrollTo({
        top: this.messagesContainer.scrollHeight,
        behavior: "smooth",
      });
    }
  }

  /**
   * Toggle compact mode
   */
  toggleCompactMode() {
    this.options.compactMode = !this.options.compactMode;
    const container = this.container.querySelector(
      ".transcription-ui-container"
    );

    if (this.options.compactMode) {
      container.classList.add("compact");
    } else {
      container.classList.remove("compact");
    }

    console.log(
      "📋 Compact mode:",
      this.options.compactMode ? "enabled" : "disabled"
    );
  }

  /**
   * Clear all messages
   */
  clearMessages() {
    // Clear visual messages
    const messages = this.messagesContainer.querySelectorAll(
      ".transcription-message"
    );
    messages.forEach((message) => message.remove());

    // Clear state
    this.visibleMessages = [];
    this.activeInterimMessages.clear();

    // Show welcome message
    this.showWelcomeMessage();

    // Clear sidebars
    this.participantsList.innerHTML = "";
    this.speakersList.innerHTML = "";

    // Update stats
    this.updateStats();

    console.log("🗑️ Messages cleared");
  }

  /**
   * Show welcome message
   */
  showWelcomeMessage() {
    const welcomeDiv = document.createElement("div");
    welcomeDiv.className = "welcome-message";
    welcomeDiv.innerHTML = `
      <div class="system-message">
        <span class="message-icon">🎤</span>
        <span class="message-text">Ready for transcription...</span>
      </div>
    `;
    this.messagesContainer.appendChild(welcomeDiv);
  }

  /**
   * Export transcription data
   */
  exportData() {
    const data = {
      messages: this.visibleMessages.map((element) => ({
        id: element.dataset.messageId,
        text: element.querySelector(".message-content").textContent,
        timestamp: element.querySelector(".timestamp")?.textContent,
        speaker: element.querySelector(".speaker-name")?.textContent,
        confidence: element.querySelector(".confidence-badge")?.textContent,
      })),
      participants: Array.from(this.participantsList.children).map(
        (element) => ({
          id: element.dataset.participantId,
          name: element.querySelector(".participant-name").textContent,
          stats: element.querySelector(".participant-stats").textContent,
        })
      ),
      speakers: Array.from(this.speakersList.children).map((element) => ({
        id: element.dataset.speakerId,
        name: element.querySelector(".speaker-name").textContent,
        stats: element.querySelector(".speaker-stats").textContent,
      })),
      exportedAt: new Date().toISOString(),
    };

    // Create and download file
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `transcription-${new Date().toISOString().split("T")[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log("💾 Data exported");
  }

  /**
   * Handle session start
   */
  handleSessionStart(data) {
    console.log("🚀 Session started:", data.participant);
    this.updateParticipantActivity(data.participantInfo);
  }

  /**
   * Handle session end
   */
  handleSessionEnd(data) {
    console.log("🛑 Session ended:", data.participant);

    // Remove participant from active list
    const participantElement = this.participantsList.querySelector(
      `[data-participant-id="${data.participant}"]`
    );
    if (participantElement) {
      participantElement.classList.remove("active");
    }
  }

  /**
   * Handle speaker change
   */
  handleSpeakerChange(data) {
    console.log("👤 Speaker changed:", data.speakerId);
    this.updateSpeakerActivity(data.speakerInfo);
  }

  /**
   * Handle interim expiration
   */
  handleInterimExpired(data) {
    console.log("⏰ Interim expired:", data.trackId);
    this.removeInterimMessage(data.trackId);
  }

  /**
   * Update options
   */
  updateOptions(newOptions) {
    this.options = { ...this.options, ...newOptions };
    console.log("⚙️ UI options updated:", newOptions);
  }

  /**
   * Destroy UI manager
   */
  destroy() {
    // Clear timers and intervals
    this.clearMessages();

    // Remove styles
    const styles = document.getElementById("transcription-ui-styles");
    if (styles) {
      styles.remove();
    }

    console.log("🗑️ TranscriptionUIManager destroyed");
  }
}

// Export for use in other modules
if (typeof module !== "undefined" && module.exports) {
  module.exports = TranscriptionUIManager;
} else if (typeof window !== "undefined") {
  window.TranscriptionUIManager = TranscriptionUIManager;
}
