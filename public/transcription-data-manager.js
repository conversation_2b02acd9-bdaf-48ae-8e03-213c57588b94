/**
 * TranscriptionDataManager - Enhanced transcription data handling
 * Manages interim/final results, speaker diarization, and multi-participant transcriptions
 */

class TranscriptionDataManager {
  constructor(options = {}) {
    this.options = {
      maxInterimAge: 5000,
      maxHistorySize: 1000,
      enableSpeakerDiarization: true,
      enableMultiParticipant: true,
      confidenceThreshold: 0.5,
      ...options,
    };

    this.interimResults = new Map();
    this.finalResults = [];
    this.participantData = new Map();
    this.speakerProfiles = new Map();
    this.activeParticipants = new Set();
    this.currentSessions = new Map();
    this.eventListeners = new Map();

    this.stats = {
      totalTranscriptions: 0,
      interimCount: 0,
      finalCount: 0,
      participantCount: 0,
      averageConfidence: 0,
      languageDistribution: new Map(),
      speakerDistribution: new Map(),
    };

    this.cleanupTimer = setInterval(() => this.cleanupExpiredInterim(), 1000);
    console.log("📊 TranscriptionDataManager initialized");
  }

  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  processTranscriptionData(data) {
    if (!this.validateTranscriptionData(data)) {
      return false;
    }

    this.updateParticipantTracking(data);

    switch (data.type) {
      case "transcription":
        return this.handleTranscriptionMessage(data);
      case "interim_transcript":
      case "interim":
        return this.handleInterimResult(data);
      case "final_transcript":
      case "final":
        return this.handleFinalResult(data);
      case "session_start":
        return this.handleSessionStart(data);
      case "session_end":
        return this.handleSessionEnd(data);
      default:
        return false;
    }
  }

  handleTranscriptionMessage(data) {
    if (data.isFinal === false) {
      return this.handleInterimResult(data);
    } else {
      return this.handleFinalResult(data);
    }
  }

  handleInterimResult(data) {
    const trackId = this.generateTrackId(data);
    const processedData = this.enrichTranscriptionData(data, "interim");

    this.interimResults.set(trackId, {
      ...processedData,
      timestamp: Date.now(),
      trackId,
    });

    this.stats.interimCount++;
    this.emit("interimResult", {
      trackId,
      data: processedData,
      participantInfo: this.getParticipantInfo(data.participant),
    });

    return true;
  }

  handleFinalResult(data) {
    const trackId = this.generateTrackId(data);
    const processedData = this.enrichTranscriptionData(data, "final");

    const interimData = this.interimResults.get(trackId);
    if (interimData) {
      this.interimResults.delete(trackId);
    }

    this.finalResults.push({
      ...processedData,
      id: this.generateUniqueId(),
      trackId,
      timestamp: Date.now(),
    });

    if (this.finalResults.length > this.options.maxHistorySize) {
      this.finalResults.shift();
    }

    this.stats.finalCount++;
    this.stats.totalTranscriptions++;
    this.updateConfidenceStats(processedData.confidence);

    this.emit("finalResult", {
      trackId,
      data: processedData,
      participantInfo: this.getParticipantInfo(data.participant),
      interimData,
    });

    return true;
  }

  handleSessionStart(data) {
    const participantId = data.participant;
    this.activeParticipants.add(participantId);
    this.currentSessions.set(participantId, {
      startTime: Date.now(),
      transcriptionCount: 0,
      lastActivity: Date.now(),
    });

    this.stats.participantCount = this.activeParticipants.size;
    this.emit("sessionStart", {
      participant: participantId,
      participantInfo: this.getParticipantInfo(participantId),
    });

    return true;
  }

  handleSessionEnd(data) {
    const participantId = data.participant;
    this.activeParticipants.delete(participantId);
    const sessionData = this.currentSessions.get(participantId);
    this.currentSessions.delete(participantId);

    this.stats.participantCount = this.activeParticipants.size;
    this.emit("sessionEnd", {
      participant: participantId,
      sessionData,
      participantInfo: this.getParticipantInfo(participantId),
    });

    return true;
  }

  validateTranscriptionData(data) {
    if (!data || typeof data !== "object") return false;
    if (!data.type) return false;

    if (
      [
        "transcription",
        "interim_transcript",
        "final_transcript",
        "interim",
        "final",
      ].includes(data.type)
    ) {
      if (
        !data.text ||
        typeof data.text !== "string" ||
        data.text.trim().length === 0
      ) {
        return false;
      }
    }

    return true;
  }

  enrichTranscriptionData(data, resultType) {
    return {
      ...data,
      resultType,
      processedAt: Date.now(),
      confidence: this.normalizeConfidence(data.confidence),
      language: data.language || "unknown",
      speaker: this.processSpeakerData(data.speaker, data.participant),
      metadata: {
        ...data.metadata,
        processingLatency: Date.now() - (data.timestamp || Date.now()),
        textLength: data.text ? data.text.length : 0,
        wordCount: data.text ? data.text.split(/\s+/).length : 0,
      },
    };
  }

  processSpeakerData(speakerData, participantId) {
    if (!this.options.enableSpeakerDiarization) {
      return {
        id: 0,
        name: "Speaker",
        confidence: 1.0,
        participant: participantId,
      };
    }

    if (!speakerData) {
      return {
        id: 0,
        name: "Speaker",
        confidence: 0.8,
        participant: participantId,
      };
    }

    let speakerId, confidence;
    if (typeof speakerData === "object") {
      speakerId =
        speakerData.speaker !== undefined
          ? speakerData.speaker
          : speakerData.id || 0;
      confidence = speakerData.confidence || 0.8;
    } else {
      speakerId = speakerData;
      confidence = 0.8;
    }

    const speakerProfile = this.updateSpeakerProfile({
      id: speakerId,
      confidence,
      participant: participantId,
    });

    return {
      id: speakerId,
      name: speakerProfile.name,
      confidence,
      participant: participantId,
      profile: speakerProfile,
    };
  }

  updateSpeakerProfile(speakerData) {
    const speakerId = speakerData.id;
    const participantId = speakerData.participant;

    if (!this.speakerProfiles.has(speakerId)) {
      const speakerName = this.generateSpeakerName(speakerId);
      this.speakerProfiles.set(speakerId, {
        id: speakerId,
        name: speakerName,
        firstSeen: Date.now(),
        lastSeen: Date.now(),
        transcriptionCount: 0,
        participants: new Set([participantId]),
        averageConfidence: speakerData.confidence || 0.8,
        color: this.getSpeakerColor(speakerId),
        icon: this.getSpeakerIcon(speakerId),
      });
    }

    const profile = this.speakerProfiles.get(speakerId);
    profile.lastSeen = Date.now();
    profile.transcriptionCount++;
    profile.participants.add(participantId);

    const currentAvg = profile.averageConfidence;
    const newConfidence = speakerData.confidence || 0.8;
    profile.averageConfidence = (currentAvg + newConfidence) / 2;

    return profile;
  }

  generateSpeakerName(speakerId) {
    const speakerNames = [
      "Speaker A",
      "Speaker B",
      "Speaker C",
      "Speaker D",
      "Speaker E",
    ];
    if (speakerId < speakerNames.length) {
      return speakerNames[speakerId];
    }
    return `Speaker ${String.fromCharCode(65 + (speakerId % 26))}`;
  }

  getSpeakerColor(speakerId) {
    const colors = [
      "#007bff",
      "#28a745",
      "#ffc107",
      "#dc3545",
      "#6f42c1",
      "#fd7e14",
      "#20c997",
      "#e83e8c",
    ];
    return colors[speakerId % colors.length];
  }

  getSpeakerIcon(speakerId) {
    const icons = ["👤", "👥", "🗣️", "💬", "🎤", "👨", "👩", "🧑"];
    return icons[speakerId % icons.length];
  }

  updateParticipantTracking(data) {
    if (!data.participant) return;

    const participantId = data.participant;
    if (!this.participantData.has(participantId)) {
      this.participantData.set(participantId, {
        id: participantId,
        name: this.generateParticipantName(participantId),
        firstSeen: Date.now(),
        lastSeen: Date.now(),
        transcriptionCount: 0,
        languages: new Set(),
        speakers: new Set(),
      });
    }

    const participant = this.participantData.get(participantId);
    participant.lastSeen = Date.now();
    participant.transcriptionCount++;

    if (data.language) participant.languages.add(data.language);
    if (data.speaker && data.speaker.id !== undefined)
      participant.speakers.add(data.speaker.id);
  }

  generateParticipantName(participantId) {
    if (participantId.includes("user-")) {
      return `User ${participantId.split("user-")[1].substring(0, 4)}`;
    }
    return `Participant ${participantId.substring(0, 8)}`;
  }

  getParticipantInfo(participantId) {
    return (
      this.participantData.get(participantId) || {
        id: participantId,
        name: this.generateParticipantName(participantId),
        transcriptionCount: 0,
      }
    );
  }

  generateTrackId(data) {
    const participant = data.participant || "unknown";
    const timeWindow = Math.floor((data.timestamp || Date.now()) / 2000) * 2000;
    const speaker = data.speaker?.id || 0;
    return `${participant}-${speaker}-${timeWindow}`;
  }

  generateUniqueId() {
    return `transcription-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;
  }

  normalizeConfidence(confidence) {
    if (confidence === undefined || confidence === null) return 0.8;
    if (typeof confidence === "string") confidence = parseFloat(confidence);
    return Math.max(0, Math.min(1, confidence));
  }

  cleanupExpiredInterim() {
    const now = Date.now();
    const expiredTracks = [];

    for (const [trackId, data] of this.interimResults.entries()) {
      if (now - data.timestamp > this.options.maxInterimAge) {
        expiredTracks.push(trackId);
      }
    }

    expiredTracks.forEach((trackId) => {
      const data = this.interimResults.get(trackId);
      this.interimResults.delete(trackId);
      this.emit("interimExpired", { trackId, data });
    });
  }

  updateConfidenceStats(confidence) {
    const currentAvg = this.stats.averageConfidence;
    const count = this.stats.totalTranscriptions;
    this.stats.averageConfidence =
      (currentAvg * (count - 1) + confidence) / count;
  }

  getStatistics() {
    return {
      ...this.stats,
      activeParticipants: this.activeParticipants.size,
      totalSpeakers: this.speakerProfiles.size,
      interimResultsCount: this.interimResults.size,
      finalResultsCount: this.finalResults.length,
      languageDistribution: Object.fromEntries(this.stats.languageDistribution),
      speakerDistribution: Object.fromEntries(this.stats.speakerDistribution),
    };
  }

  getFinalResults() {
    return [...this.finalResults];
  }

  getInterimResults() {
    return Array.from(this.interimResults.values());
  }

  clear() {
    this.interimResults.clear();
    this.finalResults.length = 0;
    this.participantData.clear();
    this.speakerProfiles.clear();
    this.activeParticipants.clear();
    this.currentSessions.clear();

    this.stats = {
      totalTranscriptions: 0,
      interimCount: 0,
      finalCount: 0,
      participantCount: 0,
      averageConfidence: 0,
      languageDistribution: new Map(),
      speakerDistribution: new Map(),
    };

    this.emit("cleared");
  }

  exportData() {
    return {
      finalResults: this.finalResults,
      participantData: Array.from(this.participantData.entries()),
      speakerProfiles: Array.from(this.speakerProfiles.entries()),
      stats: {
        ...this.stats,
        languageDistribution: Array.from(
          this.stats.languageDistribution.entries()
        ),
        speakerDistribution: Array.from(
          this.stats.speakerDistribution.entries()
        ),
      },
      exportedAt: Date.now(),
    };
  }

  importData(data) {
    if (!data || typeof data !== "object") return false;

    try {
      if (data.finalResults) this.finalResults = data.finalResults;
      if (data.participantData)
        this.participantData = new Map(data.participantData);
      if (data.speakerProfiles)
        this.speakerProfiles = new Map(data.speakerProfiles);
      if (data.stats) {
        this.stats = {
          ...data.stats,
          languageDistribution: new Map(data.stats.languageDistribution || []),
          speakerDistribution: new Map(data.stats.speakerDistribution || []),
        };
      }

      this.emit("dataImported", data);
      return true;
    } catch (error) {
      console.error("Error importing data:", error);
      return false;
    }
  }

  destroy() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.clear();
    this.eventListeners.clear();
  }
}

// Export for use in other modules
if (typeof module !== "undefined" && module.exports) {
  module.exports = TranscriptionDataManager;
  module.exports.TranscriptionDataManager = TranscriptionDataManager;
} else if (typeof window !== "undefined") {
  window.TranscriptionDataManager = TranscriptionDataManager;
}

// 浏览器环境下不使用ES6 export
// export { TranscriptionDataManager };
// export default TranscriptionDataManager;
