/**
 * Frontend Error Display Manager
 * Provides user-friendly error displays with retry options and suggestions
 */

class ErrorDisplayManager {
  constructor(options = {}) {
    this.options = {
      containerSelector: "#errorContainer",
      maxVisibleErrors: 3,
      autoHideDelay: 10000, // 10 seconds
      enableAnimations: true,
      enableSounds: false,
      position: "top-right", // top-right, top-left, bottom-right, bottom-left, center
      ...options,
    };

    this.activeErrors = new Map();
    this.errorHistory = [];
    this.container = null;

    this.init();
  }

  init() {
    this.createContainer();
    this.setupStyles();
    this.setupEventListeners();
  }

  createContainer() {
    // Remove existing container if it exists
    const existing = document.querySelector(this.options.containerSelector);
    if (existing) {
      existing.remove();
    }

    // Create new container
    this.container = document.createElement("div");
    this.container.id = "errorContainer";
    this.container.className = `error-display-container ${this.options.position}`;

    document.body.appendChild(this.container);
  }

  setupStyles() {
    // Check if styles already exist
    if (document.getElementById("error-display-styles")) {
      return;
    }

    const styles = document.createElement("style");
    styles.id = "error-display-styles";
    styles.textContent = `
      .error-display-container {
        position: fixed;
        z-index: 10000;
        pointer-events: none;
        max-width: 400px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .error-display-container.top-right {
        top: 20px;
        right: 20px;
      }

      .error-display-container.top-left {
        top: 20px;
        left: 20px;
      }

      .error-display-container.bottom-right {
        bottom: 20px;
        right: 20px;
      }

      .error-display-container.bottom-left {
        bottom: 20px;
        left: 20px;
      }

      .error-display-container.center {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 500px;
      }

      .error-notification {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        margin-bottom: 12px;
        overflow: hidden;
        pointer-events: auto;
        transform: translateX(100%);
        transition: all 0.3s ease;
        border-left: 4px solid #dc3545;
      }

      .error-notification.show {
        transform: translateX(0);
      }

      .error-notification.severity-low {
        border-left-color: #17a2b8;
      }

      .error-notification.severity-medium {
        border-left-color: #ffc107;
      }

      .error-notification.severity-high {
        border-left-color: #fd7e14;
      }

      .error-notification.severity-critical {
        border-left-color: #dc3545;
        animation: pulse-critical 2s infinite;
      }

      @keyframes pulse-critical {
        0%, 100% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); }
        50% { box-shadow: 0 4px 20px rgba(220, 53, 69, 0.3); }
      }

      .error-header {
        padding: 16px 16px 12px 16px;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
      }

      .error-icon {
        font-size: 20px;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .error-content {
        flex: 1;
        min-width: 0;
      }

      .error-title {
        font-weight: 600;
        font-size: 14px;
        color: #212529;
        margin: 0 0 4px 0;
        line-height: 1.3;
      }

      .error-message {
        font-size: 13px;
        color: #6c757d;
        margin: 0;
        line-height: 1.4;
      }

      .error-close {
        background: none;
        border: none;
        font-size: 18px;
        color: #adb5bd;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: all 0.2s ease;
        flex-shrink: 0;
      }

      .error-close:hover {
        background: #f8f9fa;
        color: #495057;
      }

      .error-details {
        padding: 0 16px 12px 16px;
        border-top: 1px solid #e9ecef;
        margin-top: 8px;
      }

      .error-suggestions {
        margin: 8px 0 0 0;
      }

      .error-suggestions-title {
        font-size: 12px;
        font-weight: 600;
        color: #495057;
        margin: 0 0 6px 0;
      }

      .error-suggestions-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .error-suggestions-list li {
        font-size: 12px;
        color: #6c757d;
        margin: 2px 0;
        padding-left: 16px;
        position: relative;
      }

      .error-suggestions-list li:before {
        content: "•";
        position: absolute;
        left: 0;
        color: #adb5bd;
      }

      .error-actions {
        padding: 12px 16px;
        background: #f8f9fa;
        display: flex;
        gap: 8px;
        justify-content: flex-end;
      }

      .error-action-btn {
        padding: 6px 12px;
        border: 1px solid #dee2e6;
        background: white;
        color: #495057;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .error-action-btn:hover {
        background: #e9ecef;
        border-color: #adb5bd;
      }

      .error-action-btn.primary {
        background: #007bff;
        color: white;
        border-color: #007bff;
      }

      .error-action-btn.primary:hover {
        background: #0056b3;
        border-color: #0056b3;
      }

      .error-progress {
        height: 2px;
        background: #e9ecef;
        overflow: hidden;
      }

      .error-progress-bar {
        height: 100%;
        background: #007bff;
        transition: width 0.1s linear;
      }

      .error-retry-info {
        font-size: 11px;
        color: #6c757d;
        margin-top: 4px;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .error-retry-spinner {
        width: 12px;
        height: 12px;
        border: 2px solid #e9ecef;
        border-top: 2px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .error-collapsed {
        max-height: 60px;
        overflow: hidden;
      }

      .error-expand-btn {
        background: none;
        border: none;
        color: #007bff;
        font-size: 12px;
        cursor: pointer;
        padding: 4px 0;
        text-decoration: underline;
      }

      .error-expand-btn:hover {
        color: #0056b3;
      }

      /* Mobile responsive */
      @media (max-width: 480px) {
        .error-display-container {
          left: 10px !important;
          right: 10px !important;
          max-width: none;
        }

        .error-display-container.center {
          transform: translate(0, -50%);
          left: 10px;
        }
      }
    `;

    document.head.appendChild(styles);
  }

  setupEventListeners() {
    // Listen for global error events if available
    if (window.transcriptionApp && window.transcriptionApp.dataManager) {
      window.transcriptionApp.dataManager.on("error", (errorData) => {
        this.showError(errorData.error, errorData.context);
      });
    }

    // Listen for unhandled promise rejections
    window.addEventListener("unhandledrejection", (event) => {
      this.showError(event.reason, { type: "unhandled_rejection" });
    });

    // Listen for global errors
    window.addEventListener("error", (event) => {
      this.showError(event.error || event.message, {
        type: "global_error",
        filename: event.filename,
        lineno: event.lineno,
      });
    });
  }

  /**
   * Show an error notification
   */
  showError(error, context = {}, options = {}) {
    const errorId = this.generateErrorId();
    const normalizedError = this.normalizeError(error, context);

    // Check if we should show this error (avoid spam)
    if (this.shouldSuppressError(normalizedError)) {
      return errorId;
    }

    const errorElement = this.createErrorElement(
      normalizedError,
      errorId,
      options
    );

    // Add to active errors
    this.activeErrors.set(errorId, {
      error: normalizedError,
      element: errorElement,
      timestamp: Date.now(),
      context,
    });

    // Add to history
    this.errorHistory.unshift({
      id: errorId,
      error: normalizedError,
      timestamp: Date.now(),
      context,
    });

    // Limit history size
    if (this.errorHistory.length > 50) {
      this.errorHistory = this.errorHistory.slice(0, 50);
    }

    // Add to container
    this.container.appendChild(errorElement);

    // Trigger show animation
    requestAnimationFrame(() => {
      errorElement.classList.add("show");
    });

    // Auto-hide for non-critical errors
    if (
      normalizedError.severity !== "critical" &&
      this.options.autoHideDelay > 0
    ) {
      setTimeout(() => {
        this.hideError(errorId);
      }, this.options.autoHideDelay);
    }

    // Limit visible errors
    this.limitVisibleErrors();

    // Play sound if enabled
    if (this.options.enableSounds) {
      this.playErrorSound(normalizedError.severity);
    }

    return errorId;
  }

  /**
   * Create error element
   */
  createErrorElement(error, errorId, options = {}) {
    const element = document.createElement("div");
    element.className = `error-notification severity-${error.severity}`;
    element.dataset.errorId = errorId;

    const icon = this.getErrorIcon(error.severity);
    const canRetry = error.retryable && options.retryFunction;
    const showDetails = error.suggestions && error.suggestions.length > 0;

    element.innerHTML = `
      <div class="error-header">
        <div class="error-icon">${icon}</div>
        <div class="error-content">
          <div class="error-title">${error.userMessage || error.message}</div>
          <div class="error-message">${error.type}</div>
          ${
            options.retryAttempt
              ? `
            <div class="error-retry-info">
              <div class="error-retry-spinner"></div>
              重试中... (${options.retryAttempt}/${options.maxRetries})
            </div>
          `
              : ""
          }
        </div>
        <button class="error-close" onclick="errorDisplayManager.hideError('${errorId}')">&times;</button>
      </div>
      
      ${
        showDetails
          ? `
        <div class="error-details">
          <div class="error-suggestions">
            <div class="error-suggestions-title">建议解决方案:</div>
            <ul class="error-suggestions-list">
              ${error.suggestions
                .map((suggestion) => `<li>${suggestion}</li>`)
                .join("")}
            </ul>
          </div>
        </div>
      `
          : ""
      }
      
      ${
        canRetry || showDetails
          ? `
        <div class="error-actions">
          ${
            showDetails
              ? `<button class="error-action-btn" onclick="errorDisplayManager.toggleDetails('${errorId}')">详情</button>`
              : ""
          }
          ${
            canRetry
              ? `<button class="error-action-btn primary" onclick="errorDisplayManager.retryError('${errorId}')">重试</button>`
              : ""
          }
        </div>
      `
          : ""
      }
    `;

    return element;
  }

  /**
   * Hide error notification
   */
  hideError(errorId) {
    const errorData = this.activeErrors.get(errorId);
    if (!errorData) return;

    const element = errorData.element;
    element.classList.remove("show");

    setTimeout(() => {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
      this.activeErrors.delete(errorId);
    }, 300);
  }

  /**
   * Hide all error notifications
   */
  hideAllErrors() {
    for (const errorId of this.activeErrors.keys()) {
      this.hideError(errorId);
    }
  }

  /**
   * Retry an error
   */
  async retryError(errorId) {
    const errorData = this.activeErrors.get(errorId);
    if (!errorData || !errorData.retryFunction) return;

    const element = errorData.element;
    const retryBtn = element.querySelector(".error-action-btn.primary");

    if (retryBtn) {
      retryBtn.disabled = true;
      retryBtn.textContent = "重试中...";
    }

    try {
      await errorData.retryFunction();
      this.hideError(errorId);

      // Show success message
      this.showSuccess("操作重试成功");
    } catch (retryError) {
      // Show new error for retry failure
      this.showError(retryError, { ...errorData.context, isRetry: true });
    } finally {
      if (retryBtn) {
        retryBtn.disabled = false;
        retryBtn.textContent = "重试";
      }
    }
  }

  /**
   * Toggle error details
   */
  toggleDetails(errorId) {
    const errorData = this.activeErrors.get(errorId);
    if (!errorData) return;

    const element = errorData.element;
    const details = element.querySelector(".error-details");
    const btn = element.querySelector(".error-expand-btn");

    if (details.style.display === "none") {
      details.style.display = "block";
      if (btn) btn.textContent = "收起";
    } else {
      details.style.display = "none";
      if (btn) btn.textContent = "展开";
    }
  }

  /**
   * Show success message
   */
  showSuccess(message, duration = 3000) {
    const successElement = document.createElement("div");
    successElement.className = "error-notification severity-low";
    successElement.style.borderLeftColor = "#28a745";

    successElement.innerHTML = `
      <div class="error-header">
        <div class="error-icon">✅</div>
        <div class="error-content">
          <div class="error-title">${message}</div>
        </div>
        <button class="error-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
      </div>
    `;

    this.container.appendChild(successElement);

    requestAnimationFrame(() => {
      successElement.classList.add("show");
    });

    setTimeout(() => {
      if (successElement.parentNode) {
        successElement.classList.remove("show");
        setTimeout(() => {
          if (successElement.parentNode) {
            successElement.parentNode.removeChild(successElement);
          }
        }, 300);
      }
    }, duration);
  }

  /**
   * Normalize error object
   */
  normalizeError(error, context = {}) {
    if (typeof error === "string") {
      return {
        type: "UNKNOWN_ERROR",
        message: error,
        userMessage: error,
        severity: "medium",
        retryable: false,
        suggestions: ["稍后重试", "刷新页面"],
      };
    }

    if (error && typeof error === "object") {
      return {
        type: error.type || "UNKNOWN_ERROR",
        message: error.message || "未知错误",
        userMessage: error.userMessage || error.message || "发生了未知错误",
        severity: error.severity || "medium",
        retryable: error.retryable !== false,
        suggestions: error.suggestions || ["稍后重试"],
        code: error.code,
        context: error.context || context,
      };
    }

    return {
      type: "UNKNOWN_ERROR",
      message: "未知错误",
      userMessage: "发生了未知错误",
      severity: "medium",
      retryable: false,
      suggestions: ["稍后重试", "刷新页面"],
    };
  }

  /**
   * Check if error should be suppressed (avoid spam)
   */
  shouldSuppressError(error) {
    const recentErrors = this.errorHistory.slice(0, 5);
    const duplicateCount = recentErrors.filter(
      (e) => e.error.type === error.type && e.error.message === error.message
    ).length;

    // Suppress if we've seen this error 3+ times in the last 5 errors
    return duplicateCount >= 3;
  }

  /**
   * Get error icon based on severity
   */
  getErrorIcon(severity) {
    const icons = {
      low: "ℹ️",
      medium: "⚠️",
      high: "❌",
      critical: "🚨",
    };
    return icons[severity] || "❌";
  }

  /**
   * Limit visible errors to prevent UI overflow
   */
  limitVisibleErrors() {
    const visibleErrors = Array.from(this.container.children);
    if (visibleErrors.length > this.options.maxVisibleErrors) {
      const excess = visibleErrors.slice(this.options.maxVisibleErrors);
      excess.forEach((element) => {
        const errorId = element.dataset.errorId;
        if (errorId) {
          this.hideError(errorId);
        }
      });
    }
  }

  /**
   * Play error sound
   */
  playErrorSound(severity) {
    if (!this.options.enableSounds) return;

    // Create audio context if not exists
    if (!this.audioContext) {
      try {
        this.audioContext = new (window.AudioContext ||
          window.webkitAudioContext)();
      } catch (e) {
        return; // Audio not supported
      }
    }

    // Play different tones based on severity
    const frequencies = {
      low: 440,
      medium: 523,
      high: 659,
      critical: 880,
    };

    const frequency = frequencies[severity] || 523;
    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.frequency.setValueAtTime(
      frequency,
      this.audioContext.currentTime
    );
    oscillator.type = "sine";

    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(
      0.01,
      this.audioContext.currentTime + 0.3
    );

    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + 0.3);
  }

  /**
   * Generate unique error ID
   */
  generateErrorId() {
    return (
      "error_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9)
    );
  }

  /**
   * Get error statistics
   */
  getStats() {
    return {
      active: this.activeErrors.size,
      total: this.errorHistory.length,
      byType: this.getErrorCountByType(),
      bySeverity: this.getErrorCountBySeverity(),
      recent: this.errorHistory.slice(0, 10),
    };
  }

  getErrorCountByType() {
    const counts = {};
    this.errorHistory.forEach((item) => {
      const type = item.error.type;
      counts[type] = (counts[type] || 0) + 1;
    });
    return counts;
  }

  getErrorCountBySeverity() {
    const counts = {};
    this.errorHistory.forEach((item) => {
      const severity = item.error.severity;
      counts[severity] = (counts[severity] || 0) + 1;
    });
    return counts;
  }

  /**
   * Clear all errors and history
   */
  clear() {
    this.hideAllErrors();
    this.errorHistory = [];
  }
}

// Create global instance
window.errorDisplayManager = new ErrorDisplayManager();

// Export for module usage
if (typeof module !== "undefined" && module.exports) {
  module.exports = ErrorDisplayManager;
}
