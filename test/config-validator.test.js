#!/usr/bin/env node

/**
 * Test script for ConfigValidator
 * Tests validation logic, connectivity tests, and setup guide generation
 */

import { ConfigValidator } from "../src/config-validator.js";

// Mock environment variables for testing
const originalEnv = { ...process.env };

function setTestEnv(envVars) {
  // Clear existing env vars
  delete process.env.LIVEKIT_URL;
  delete process.env.LIVEKIT_API_KEY;
  delete process.env.LIVEKIT_API_SECRET;
  delete process.env.DEEPGRAM_API_KEY;
  delete process.env.OPENAI_API_KEY;

  // Set test env vars
  Object.assign(process.env, envVars);
}

function restoreEnv() {
  process.env = { ...originalEnv };
}

async function runTests() {
  console.log("🧪 Starting ConfigValidator tests...\n");

  // Test 1: Missing required environment variables
  console.log("Test 1: Missing required environment variables");
  setTestEnv({});

  const validator1 = new ConfigValidator({ skipConnectivityTests: true });
  const results1 = await validator1.validate();

  console.log(
    `Result: ${results1.overall ? "PASSED" : "FAILED"} (expected: FAILED)`
  );
  console.log(`Errors: ${results1.errors.length} (expected: 4)`);
  console.log(
    `Setup guide available: ${!!results1.setupGuide} (expected: true)\n`
  );

  // Test 2: Valid configuration (mock values)
  console.log("Test 2: Valid configuration format");
  setTestEnv({
    LIVEKIT_URL: "wss://test.livekit.cloud",
    LIVEKIT_API_KEY: "APItest123456789012345",
    LIVEKIT_API_SECRET: "testsecret123456789012345678901234567890",
    DEEPGRAM_API_KEY: "deepgramtest123456789012345678901234567890",
    OPENAI_API_KEY: "sk-test123456789012345678901234567890123456",
  });

  const validator2 = new ConfigValidator({ skipConnectivityTests: true });
  const results2 = await validator2.validate();

  console.log(
    `Result: ${results2.overall ? "PASSED" : "FAILED"} (expected: PASSED)`
  );
  console.log(`Errors: ${results2.errors.length} (expected: 0)`);
  console.log(`Warnings: ${results2.warnings.length} (expected: 0 or more)\n`);

  // Test 3: Invalid URL format
  console.log("Test 3: Invalid URL format");
  setTestEnv({
    LIVEKIT_URL: "invalid-url",
    LIVEKIT_API_KEY: "APItest123456789012345",
    LIVEKIT_API_SECRET: "testsecret123456789012345678901234567890",
    DEEPGRAM_API_KEY: "deepgramtest123456789012345678901234567890",
  });

  const validator3 = new ConfigValidator({ skipConnectivityTests: true });
  const results3 = await validator3.validate();

  console.log(
    `Result: ${results3.overall ? "PASSED" : "FAILED"} (expected: FAILED)`
  );
  console.log(
    `URL validation failed: ${!results3.envVars.LIVEKIT_URL
      .valid} (expected: true)\n`
  );

  // Test 4: Invalid API key format
  console.log("Test 4: Invalid API key format");
  setTestEnv({
    LIVEKIT_URL: "wss://test.livekit.cloud",
    LIVEKIT_API_KEY: "invalid-key",
    LIVEKIT_API_SECRET: "testsecret123456789012345678901234567890",
    DEEPGRAM_API_KEY: "deepgramtest123456789012345678901234567890",
  });

  const validator4 = new ConfigValidator({ skipConnectivityTests: true });
  const results4 = await validator4.validate();

  console.log(
    `Result: ${results4.overall ? "PASSED" : "FAILED"} (expected: FAILED)`
  );
  console.log(
    `API key validation failed: ${!results4.envVars.LIVEKIT_API_KEY
      .valid} (expected: true)\n`
  );

  // Test 5: Setup guide generation
  console.log("Test 5: Setup guide generation");
  setTestEnv({
    LIVEKIT_URL: "wss://test.livekit.cloud",
    // Missing other required vars
  });

  const validator5 = new ConfigValidator({ skipConnectivityTests: true });
  const results5 = await validator5.validate();

  console.log(
    `Setup guide generated: ${!!results5.setupGuide} (expected: true)`
  );
  console.log(
    `Missing required items: ${results5.setupGuide.missingRequired.length} (expected: 3)`
  );
  console.log(
    `Quick start steps: ${results5.setupGuide.quickStart.length} (expected: 6)\n`
  );

  // Test 6: Connectivity test with timeout (should fail quickly)
  console.log("Test 6: Connectivity test with short timeout");
  setTestEnv({
    LIVEKIT_URL: "wss://nonexistent.example.com",
    LIVEKIT_API_KEY: "APItest123456789012345",
    LIVEKIT_API_SECRET: "testsecret123456789012345678901234567890",
    DEEPGRAM_API_KEY: "deepgramtest123456789012345678901234567890",
  });

  const validator6 = new ConfigValidator({
    skipConnectivityTests: false,
    timeout: 2000, // Short timeout for testing
  });

  const startTime = Date.now();
  const results6 = await validator6.validate();
  const duration = Date.now() - startTime;

  console.log(
    `Result: ${results6.overall ? "PASSED" : "FAILED"} (expected: FAILED)`
  );
  console.log(`Duration: ${duration}ms (expected: < 10000ms)`);
  console.log(
    `Connectivity tests ran: ${
      Object.keys(results6.connectivity).length > 0
    } (expected: true)\n`
  );

  // Test 7: Validation helper methods
  console.log("Test 7: Validation helper methods");
  const validator7 = new ConfigValidator();

  const urlTests = [
    { url: "https://example.com", expected: true },
    { url: "wss://test.livekit.cloud", expected: true },
    { url: "invalid-url", expected: false },
    { url: "ftp://example.com", expected: false },
  ];

  console.log("URL validation tests:");
  urlTests.forEach((test) => {
    const result = validator7.validateUrl(test.url);
    const status = result === test.expected ? "✅" : "❌";
    console.log(
      `  ${status} ${test.url}: ${result} (expected: ${test.expected})`
    );
  });

  const apiKeyTests = [
    { key: "APItest123456789012345", prefix: "API", expected: true },
    {
      key: "sk-test123456789012345678901234567890123456",
      prefix: "sk-",
      expected: true,
    },
    { key: "invalid", prefix: "API", expected: false },
    { key: "APIshort", prefix: "API", expected: false },
  ];

  console.log("API key validation tests:");
  apiKeyTests.forEach((test) => {
    const result = validator7.validateApiKey(test.key, test.prefix);
    const status = result === test.expected ? "✅" : "❌";
    console.log(
      `  ${status} ${test.key}: ${result} (expected: ${test.expected})`
    );
  });

  // Test 8: Quick validate static method
  console.log("\nTest 8: Quick validate static method");
  setTestEnv({
    LIVEKIT_URL: "wss://test.livekit.cloud",
    LIVEKIT_API_KEY: "APItest123456789012345",
    LIVEKIT_API_SECRET: "testsecret123456789012345678901234567890",
    DEEPGRAM_API_KEY: "deepgramtest123456789012345678901234567890",
  });

  const quickResults = await ConfigValidator.quickValidate({
    skipConnectivityTests: true,
  });
  console.log(
    `Quick validate result: ${
      quickResults.overall ? "PASSED" : "FAILED"
    } (expected: PASSED)`
  );

  // Restore original environment
  restoreEnv();

  console.log("\n🎉 All ConfigValidator tests completed!");
}

// Run tests
runTests().catch(console.error);
