/**
 * Integration Tests for End-to-End Transcription Flow
 * Tests the complete transcription pipeline from audio input to output
 */

import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import {
  enableConsoleMocks,
  restoreConsole,
  testUtils,
  performanceMonitor,
  TEST_CONFIG,
} from "../setup.js";

// Import components for integration testing
import { EnhancedAgentManager } from "../../src/enhanced-agent-v2.js";
import { AudioStreamProcessor } from "../../src/audio-stream-processor.js";

describe("End-to-End Transcription Flow Integration Tests", () => {
  let agentManager;
  let audioProcessor;
  let mockContext;
  let mockParticipant;
  let mockRoom;

  beforeEach(async () => {
    enableConsoleMocks();
    performanceMonitor.reset();

    // Setup test environment
    vi.stubGlobal("process", { env: testUtils.createMockEnvironment() });

    // Create mock LiveKit context
    mockRoom = testUtils.generateMockRoom();
    mockParticipant = testUtils.generateMockParticipant();
    mockContext = {
      connect: vi.fn().mockResolvedValue(true),
      room: mockRoom,
      waitForParticipant: vi.fn().mockResolvedValue(mockParticipant),
    };

    // Initialize agent manager
    agentManager = new EnhancedAgentManager();
    await agentManager.initialize();
  });

  afterEach(() => {
    restoreConsole();
    vi.restoreAllMocks();
  });

  describe("Complete Transcription Pipeline", () => {
    it("should handle complete transcription flow from start to finish", async () => {
      performanceMonitor.startTimer("complete-flow");

      // Step 1: Agent initialization
      expect(agentManager.currentAgentType).toBeTruthy();
      expect(agentManager.status.status).toBe("connected");

      // Step 2: Participant connection
      const session = await agentManager.handleParticipant(
        mockContext,
        mockParticipant
      );
      expect(session).toBeDefined();

      // Step 3: Audio track processing
      const mockTrack = testUtils.generateMockTrack();

      // Simulate track subscription event
      const trackHandler = mockRoom.on.mock.calls.find(
        (call) => call[0] === "trackSubscribed"
      );
      if (trackHandler) {
        await trackHandler[1](mockTrack, null, mockParticipant);
      }

      // Step 4: Verify transcription data flow
      const publishDataCalls = mockRoom.localParticipant.publishData.mock.calls;
      expect(publishDataCalls.length).toBeGreaterThan(0);

      // Verify transcription data structure
      const transcriptionData = JSON.parse(publishDataCalls[0][0]);
      const validation = testUtils.validateTranscriptionData(transcriptionData);
      expect(validation.valid).toBe(true);

      const flowDuration = performanceMonitor.endTimer("complete-flow");
      expect(flowDuration).toBeLessThan(TEST_CONFIG.performanceThreshold);
    });

    it("should handle multiple participants simultaneously", async () => {
      const participants = Array.from({ length: 3 }, (_, i) =>
        testUtils.generateMockParticipant(`participant-${i}`)
      );

      const sessions = await Promise.all(
        participants.map((participant) =>
          agentManager.handleParticipant(mockContext, participant)
        )
      );

      expect(sessions).toHaveLength(3);
      sessions.forEach((session) => expect(session).toBeDefined());

      // Verify metrics reflect multiple connections
      const metrics = agentManager.getMetrics();
      expect(metrics.connectionsAttempted).toBe(3);
    });

    it("should maintain transcription quality under load", async () => {
      const transcriptionPromises = Array.from({ length: 10 }, async (_, i) => {
        const participant = testUtils.generateMockParticipant(`load-test-${i}`);
        const session = await agentManager.handleParticipant(
          mockContext,
          participant
        );

        // Simulate transcription events
        if (session && session.on) {
          const mockEvent = {
            text: `Test transcription ${i}`,
            confidence: 0.9 + Math.random() * 0.1,
            isFinal: true,
          };

          // Trigger transcription event
          const eventHandler = session.on.mock.calls.find(
            (call) => call[0] === "user_speech_committed"
          );
          if (eventHandler) {
            await eventHandler[1](mockEvent);
          }
        }

        return { participant: participant.identity, session };
      });

      const results = await Promise.all(transcriptionPromises);

      expect(results).toHaveLength(10);
      results.forEach((result) => {
        expect(result.session).toBeDefined();
        expect(result.participant).toBeTruthy();
      });

      // Verify all transcriptions were published
      const publishCalls = mockRoom.localParticipant.publishData.mock.calls;
      expect(publishCalls.length).toBeGreaterThanOrEqual(10);
    });
  });

  describe("Audio Stream Processing Integration", () => {
    it("should successfully connect audio tracks through all strategies", async () => {
      // Create audio processor
      const mockSTT = {
        stream: vi.fn().mockReturnValue({
          input: {
            put: vi.fn(),
            push: vi.fn(),
            write: vi.fn(),
          },
          [Symbol.asyncIterator]: async function* () {
            yield { text: "integration test", confidence: 0.95, isFinal: true };
          },
        }),
      };

      audioProcessor = new AudioStreamProcessor(mockSTT);

      // Test each connection strategy
      const strategies = [
        "directSTTStream",
        "manualAudioExtraction",
        "fallbackBuffering",
      ];

      for (const strategy of strategies) {
        const mockTrack = testUtils.generateMockTrack(`track-${strategy}`);

        performanceMonitor.startTimer(`strategy-${strategy}`);
        const success = await audioProcessor.connectTrack(
          mockTrack,
          mockContext,
          mockParticipant
        );
        const duration = performanceMonitor.endTimer(`strategy-${strategy}`);

        expect(success).toBe(true);
        expect(duration).toBeLessThan(TEST_CONFIG.performanceThreshold);
      }
    });

    it("should handle audio stream errors gracefully", async () => {
      const mockSTT = {
        stream: vi.fn().mockReturnValue({
          input: {
            put: vi.fn().mockRejectedValue(new Error("Stream error")),
          },
          [Symbol.asyncIterator]: async function* () {
            throw new Error("Stream processing error");
          },
        }),
      };

      audioProcessor = new AudioStreamProcessor(mockSTT);

      const mockTrack = testUtils.generateMockTrack("error-track");

      // Should not throw, but handle error gracefully
      const success = await audioProcessor.connectTrack(
        mockTrack,
        mockContext,
        mockParticipant
      );

      // Depending on fallback strategies, this might still succeed
      expect(typeof success).toBe("boolean");

      // Verify error metrics were recorded
      const metrics = audioProcessor.getMetrics();
      expect(metrics.totalConnections).toBeGreaterThan(0);
    });

    it("should maintain audio quality metrics", async () => {
      const mockSTT = {
        stream: vi.fn().mockReturnValue({
          input: { put: vi.fn() },
          [Symbol.asyncIterator]: async function* () {
            // Simulate varying quality transcriptions
            yield { text: "high quality", confidence: 0.95, isFinal: false };
            yield { text: "medium quality", confidence: 0.75, isFinal: false };
            yield { text: "final result", confidence: 0.9, isFinal: true };
          },
        }),
      };

      audioProcessor = new AudioStreamProcessor(mockSTT);

      const mockTrack = testUtils.generateMockTrack("quality-track");
      await audioProcessor.connectTrack(
        mockTrack,
        mockContext,
        mockParticipant
      );

      // Allow some time for async processing
      await testUtils.delay(100);

      const metrics = audioProcessor.getMetrics();
      expect(metrics.successfulConnections).toBeGreaterThan(0);
    });
  });

  describe("Error Recovery and Fallback", () => {
    it("should recover from agent failures using fallback strategies", async () => {
      // Simulate primary agent failure
      agentManager.primaryAgent = null;
      agentManager.currentAgentType = null;
      agentManager.status.status = "error";

      // Attempt re-initialization
      const recovered = await agentManager.initialize();

      expect(recovered).toBe(true);
      expect(agentManager.currentAgentType).toBeTruthy();
      expect(agentManager.status.status).toBe("connected");

      // Verify fallback was used
      const metrics = agentManager.getMetrics();
      expect(metrics.agentInitializations).toBeGreaterThan(1);
    });

    it("should handle network interruptions gracefully", async () => {
      // Simulate network failure
      mockContext.connect = vi
        .fn()
        .mockRejectedValueOnce(new Error("Network error"))
        .mockResolvedValue(true);

      // Should retry and eventually succeed
      let connectionAttempts = 0;
      const maxAttempts = 3;

      while (connectionAttempts < maxAttempts) {
        try {
          await mockContext.connect();
          break;
        } catch (error) {
          connectionAttempts++;
          if (connectionAttempts >= maxAttempts) {
            throw error;
          }
          await testUtils.delay(100);
        }
      }

      expect(connectionAttempts).toBeLessThan(maxAttempts);
    });

    it("should maintain service availability during partial failures", async () => {
      // Simulate partial system failure
      const originalHandleParticipant = agentManager.handleParticipant;
      let failureCount = 0;

      agentManager.handleParticipant = vi
        .fn()
        .mockImplementation(async (ctx, participant) => {
          failureCount++;
          if (failureCount <= 2) {
            throw new Error("Temporary failure");
          }
          return originalHandleParticipant.call(agentManager, ctx, participant);
        });

      // Test multiple participant connections
      const results = await testUtils.runConcurrentTests(async (i) => {
        const participant = testUtils.generateMockParticipant(
          `resilience-test-${i}`
        );
        try {
          return await agentManager.handleParticipant(mockContext, participant);
        } catch (error) {
          return { error: error.message };
        }
      }, 5);

      // Should have some successes despite failures
      expect(results.successes).toBeGreaterThan(0);
      expect(results.successRate).toBeGreaterThan(0.5);
    });
  });

  describe("Performance Under Load", () => {
    it("should maintain performance with concurrent transcriptions", async () => {
      const concurrentSessions = 5;
      const transcriptionsPerSession = 10;

      performanceMonitor.startTimer("concurrent-load");

      const sessionPromises = Array.from(
        { length: concurrentSessions },
        async (_, sessionId) => {
          const participant = testUtils.generateMockParticipant(
            `load-session-${sessionId}`
          );
          const session = await agentManager.handleParticipant(
            mockContext,
            participant
          );

          // Simulate multiple transcriptions per session
          const transcriptionPromises = Array.from(
            { length: transcriptionsPerSession },
            async (_, transcriptionId) => {
              const mockEvent = {
                text: `Load test ${sessionId}-${transcriptionId}`,
                confidence: 0.8 + Math.random() * 0.2,
                isFinal: transcriptionId === transcriptionsPerSession - 1,
              };

              // Simulate transcription processing
              await testUtils.delay(10);
              return mockEvent;
            }
          );

          return Promise.all(transcriptionPromises);
        }
      );

      const results = await Promise.all(sessionPromises);
      const loadDuration = performanceMonitor.endTimer("concurrent-load");

      expect(results).toHaveLength(concurrentSessions);
      expect(loadDuration).toBeLessThan(TEST_CONFIG.performanceThreshold * 2); // Allow 2x threshold for load test

      // Verify metrics reflect the load
      const metrics = agentManager.getMetrics();
      expect(metrics.connectionsAttempted).toBe(concurrentSessions);
    });

    it("should handle memory efficiently during extended operation", async () => {
      const initialMemory = process.memoryUsage
        ? process.memoryUsage().heapUsed
        : 0;

      // Simulate extended operation
      for (let i = 0; i < 100; i++) {
        const participant = testUtils.generateMockParticipant(
          `memory-test-${i}`
        );
        await agentManager.handleParticipant(mockContext, participant);

        // Simulate some processing
        agentManager.metrics.recordTranscription(0.9, true);
        agentManager.metrics.recordConnection(true, 100);

        if (i % 10 === 0) {
          performanceMonitor.recordMemory();
        }
      }

      const finalMemory = process.memoryUsage
        ? process.memoryUsage().heapUsed
        : 0;
      const memoryGrowth = finalMemory - initialMemory;

      // Memory growth should be reasonable (less than 50MB for this test)
      expect(memoryGrowth).toBeLessThan(50 * 1024 * 1024);

      const stats = performanceMonitor.getStats();
      if (stats && stats.memory.length > 0) {
        // Memory usage should be relatively stable
        const memoryReadings = stats.memory.map((m) => m.heapUsed);
        const maxMemory = Math.max(...memoryReadings);
        const minMemory = Math.min(...memoryReadings);
        const memoryVariation = (maxMemory - minMemory) / minMemory;

        expect(memoryVariation).toBeLessThan(2.0); // Less than 200% variation
      }
    });
  });

  describe("Data Integrity", () => {
    it("should maintain transcription data integrity throughout the pipeline", async () => {
      const testTranscriptions = [
        { text: "Hello world", confidence: 0.95, isFinal: false },
        { text: "Hello world!", confidence: 0.98, isFinal: true },
        { text: "How are you?", confidence: 0.92, isFinal: true },
      ];

      const session = await agentManager.handleParticipant(
        mockContext,
        mockParticipant
      );

      // Simulate transcription events
      for (const transcription of testTranscriptions) {
        if (session && session.on) {
          const eventHandler = session.on.mock.calls.find(
            (call) =>
              call[0] ===
              (transcription.isFinal
                ? "user_speech_committed"
                : "user_speech_interim")
          );

          if (eventHandler) {
            await eventHandler[1](transcription);
          }
        }
      }

      // Verify all transcriptions were published with correct data
      const publishCalls = mockRoom.localParticipant.publishData.mock.calls;
      expect(publishCalls.length).toBeGreaterThanOrEqual(
        testTranscriptions.length
      );

      publishCalls.forEach((call, index) => {
        const publishedData = JSON.parse(call[0]);
        const validation = testUtils.validateTranscriptionData(publishedData);

        expect(validation.valid).toBe(true);
        expect(publishedData.type).toBe("transcription");
        expect(publishedData.participant).toBe(mockParticipant.identity);
        expect(publishedData.confidence).toBeGreaterThan(0);
        expect(publishedData.timestamp).toBeDefined();
      });
    });

    it("should handle special characters and unicode in transcriptions", async () => {
      const specialTexts = [
        "Hello 世界! 🌍",
        "Café naïve résumé",
        "Emoji test: 😀🎉🚀",
        "Numbers: 123.45 €",
        "Special chars: @#$%^&*()",
      ];

      const session = await agentManager.handleParticipant(
        mockContext,
        mockParticipant
      );

      for (const text of specialTexts) {
        const mockEvent = { text, confidence: 0.9, isFinal: true };

        if (session && session.on) {
          const eventHandler = session.on.mock.calls.find(
            (call) => call[0] === "user_speech_committed"
          );
          if (eventHandler) {
            await eventHandler[1](mockEvent);
          }
        }
      }

      // Verify special characters are preserved
      const publishCalls = mockRoom.localParticipant.publishData.mock.calls;

      publishCalls.forEach((call, index) => {
        const publishedData = JSON.parse(call[0]);
        if (index < specialTexts.length) {
          expect(publishedData.text).toBe(specialTexts[index]);
        }
      });
    });
  });
});
