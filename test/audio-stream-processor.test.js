#!/usr/bin/env node

/**
 * Test script for AudioStreamProcessor
 * Tests the connection strategies and retry logic
 */

import { AudioStreamProcessor } from "../src/audio-stream-processor.js";
import { EventEmitter } from "events";

// Mock STT implementation
class MockSTT {
  stream() {
    return {
      input: {
        put: (track) => {
          if (track.shouldFail) {
            throw new Error("Mock connection failure");
          }
          console.log("✅ Mock STT input.put() called successfully");
        },
        push: (track) => {
          throw new Error("Mock push method failure");
        },
      },
      [Symbol.asyncIterator]: async function* () {
        yield { text: "Hello world", isFinal: false, confidence: 0.8 };
        yield { text: "Hello world!", isFinal: true, confidence: 0.95 };
      },
    };
  }
}

// Mock track
const createMockTrack = (shouldFail = false) => ({
  info: { sid: `track_${Date.now()}` },
  kind: "audio",
  shouldFail,
  ffi_handle: shouldFail ? null : { mockHandle: true },
});

// Mock context and participant
const mockCtx = {
  room: {
    localParticipant: {
      publishData: async (data, options) => {
        console.log("📡 Mock data published:", JSON.parse(data));
        return true;
      },
    },
  },
};

const mockParticipant = {
  identity: "test-participant",
};

async function runTests() {
  console.log("🧪 Starting AudioStreamProcessor tests...\n");

  // Test 1: Successful connection
  console.log("Test 1: Successful connection");
  const stt1 = new MockSTT();
  const processor1 = new AudioStreamProcessor(stt1, {
    maxRetries: 2,
    baseDelay: 100,
    connectionTimeout: 2000,
  });

  // Set up event listeners
  processor1.on("connectionAttempt", (data) => {
    console.log("🔗 Connection attempt:", data.strategies);
  });

  processor1.on("strategyAttempt", (data) => {
    console.log(
      `🎯 Trying strategy: ${data.strategy} (${data.attempt}/${data.maxAttempts})`
    );
  });

  processor1.on("connectionSuccess", (data) => {
    console.log(`✅ Connection successful with strategy: ${data.strategy}`);
  });

  processor1.on("strategyError", (data) => {
    console.log(`❌ Strategy error: ${data.strategy} - ${data.error}`);
  });

  const track1 = createMockTrack(false);
  const success1 = await processor1.connectTrack(
    track1,
    mockCtx,
    mockParticipant
  );
  console.log(`Result: ${success1 ? "SUCCESS" : "FAILED"}\n`);

  // Test 2: Connection with retries
  console.log("Test 2: Connection with retries (first strategy fails)");
  const stt2 = new MockSTT();
  const processor2 = new AudioStreamProcessor(stt2, {
    maxRetries: 2,
    baseDelay: 100,
    connectionTimeout: 2000,
  });

  processor2.on("retryDelay", (data) => {
    console.log(`⏳ Retrying in ${data.delay}ms`);
  });

  processor2.on("strategyFailed", (data) => {
    console.log(
      `❌ Strategy ${data.strategy} failed after ${data.totalAttempts} attempts`
    );
  });

  processor2.on("connectionSuccess", (data) => {
    console.log(`✅ Connection successful with strategy: ${data.strategy}`);
  });

  const track2 = createMockTrack(true); // This will cause the first strategy to fail
  const success2 = await processor2.connectTrack(
    track2,
    mockCtx,
    mockParticipant
  );
  console.log(`Result: ${success2 ? "SUCCESS" : "FAILED"}\n`);

  // Test 3: All strategies fail
  console.log("Test 3: All strategies fail");
  const stt3 = new MockSTT();
  const processor3 = new AudioStreamProcessor(stt3, {
    maxRetries: 1,
    baseDelay: 50,
    connectionTimeout: 1000,
  });

  processor3.on("connectionFailed", (data) => {
    console.log("❌ All strategies failed:", data.allStrategiesFailed);
  });

  // Override the multimodal agent request handler to always fail
  processor3.on("multimodalAgentRequest", (data) => {
    setTimeout(() => data.callback(false), 100);
  });

  const track3 = createMockTrack(true);
  track3.ffi_handle = null; // Remove ffi_handle to make manual extraction fail
  const success3 = await processor3.connectTrack(
    track3,
    mockCtx,
    mockParticipant
  );
  console.log(`Result: ${success3 ? "SUCCESS" : "FAILED"}\n`);

  // Test 4: Metrics collection
  console.log("Test 4: Metrics collection");
  const metrics1 = processor1.getMetrics();
  console.log("Processor 1 metrics:", {
    totalConnections: metrics1.totalConnections,
    successfulConnections: metrics1.successfulConnections,
    failedConnections: metrics1.failedConnections,
    strategiesUsed: metrics1.strategiesUsed,
  });

  const metrics3 = processor3.getMetrics();
  console.log("Processor 3 metrics:", {
    totalConnections: metrics3.totalConnections,
    successfulConnections: metrics3.successfulConnections,
    failedConnections: metrics3.failedConnections,
    strategiesUsed: metrics3.strategiesUsed,
  });

  console.log("\n🎉 All tests completed!");
}

// Run tests
runTests().catch(console.error);
