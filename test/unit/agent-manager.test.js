/**
 * Unit Tests for AgentManager
 * Tests the core agent management functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import {
  enableConsoleMocks,
  restoreConsole,
  testUtils,
  TEST_CONFIG,
} from "../setup.js";

// Import the component to test
import { EnhancedAgentManager } from "../../src/enhanced-agent-v2.js";

describe("AgentManager Unit Tests", () => {
  let agentManager;
  let mockProcess;

  beforeEach(() => {
    enableConsoleMocks();

    // Create fresh agent manager instance
    agentManager = new EnhancedAgentManager();

    // Mock process.env with test environment
    mockProcess = {
      env: testUtils.createMockEnvironment(),
    };
    vi.stubGlobal("process", mockProcess);
  });

  afterEach(() => {
    restoreConsole();
    vi.restoreAllMocks();
  });

  describe("Initialization", () => {
    it("should initialize with correct default state", () => {
      expect(agentManager.currentAgentType).toBeNull();
      expect(agentManager.primaryAgent).toBeNull();
      expect(agentManager.status.status).toBe("initializing");
      expect(agentManager.agentStrategies).toHaveLength(3);
    });

    it("should have strategies in correct priority order", () => {
      const strategies = agentManager.agentStrategies;

      expect(strategies[0].name).toBe("multimodal");
      expect(strategies[0].priority).toBe(1);
      expect(strategies[1].name).toBe("multimodal-stt-only");
      expect(strategies[1].priority).toBe(2);
      expect(strategies[2].name).toBe("direct-stt");
      expect(strategies[2].priority).toBe(3);
    });

    it("should initialize successfully with full configuration", async () => {
      const result = await agentManager.initialize();

      expect(result).toBe(true);
      expect(agentManager.currentAgentType).toBe("multimodal");
      expect(agentManager.status.status).toBe("connected");
      expect(agentManager.primaryAgent).toBeDefined();
    });

    it("should fallback to STT-only when OpenAI key is missing", async () => {
      delete mockProcess.env.OPENAI_API_KEY;

      const result = await agentManager.initialize();

      expect(result).toBe(true);
      expect(agentManager.currentAgentType).toBe("multimodal-stt-only");
      expect(agentManager.status.status).toBe("connected");
    });

    it("should fail gracefully when no API keys are provided", async () => {
      mockProcess.env = {}; // No environment variables

      const result = await agentManager.initialize();

      expect(result).toBe(false);
      expect(agentManager.status.status).toBe("error");
      expect(agentManager.status.errorDetails).toBeDefined();
    });
  });

  describe("Beta Version Detection", () => {
    it("should detect beta versions correctly", () => {
      mockProcess.env.LIVEKIT_AGENTS_VERSION = "1.0.0-beta.1";
      expect(agentManager.isBetaVersion()).toBe(true);

      mockProcess.env.LIVEKIT_AGENTS_VERSION = "1.0.0-alpha.1";
      expect(agentManager.isBetaVersion()).toBe(true);

      mockProcess.env.LIVEKIT_AGENTS_VERSION = "1.0.0-rc.1";
      expect(agentManager.isBetaVersion()).toBe(true);

      mockProcess.env.LIVEKIT_AGENTS_VERSION = "1.0.0";
      expect(agentManager.isBetaVersion()).toBe(false);
    });

    it("should handle missing version gracefully", () => {
      delete mockProcess.env.LIVEKIT_AGENTS_VERSION;
      expect(agentManager.isBetaVersion()).toBe(false);
    });
  });

  describe("Strategy Selection", () => {
    it("should select multimodal strategy with full configuration", async () => {
      const strategy = agentManager.agentStrategies[0]; // multimodal
      const result = await agentManager.initializeWithStrategy(strategy);

      expect(result).toBe(true);
    });

    it("should handle strategy requirements validation", async () => {
      const strategy = {
        name: "test-strategy",
        requirements: ["MISSING_API_KEY"],
      };

      await expect(
        agentManager.initializeWithStrategy(strategy)
      ).rejects.toThrow(
        "Missing required environment variables: MISSING_API_KEY"
      );
    });
  });

  describe("Status and Metrics", () => {
    it("should provide comprehensive status information", async () => {
      await agentManager.initialize();

      const status = agentManager.getStatus();

      expect(status).toHaveProperty("status");
      expect(status).toHaveProperty("agentType");
      expect(status).toHaveProperty("lastActivity");
      expect(status).toHaveProperty("metrics");
      expect(status).toHaveProperty("correlationId");
      expect(status).toHaveProperty("initializationAttempts");
      expect(status).toHaveProperty("fallbacksUsed");
    });

    it("should track metrics during initialization", async () => {
      await agentManager.initialize();

      const metrics = agentManager.getMetrics();
      expect(metrics.agentInitializations).toBe(1);
      expect(metrics.currentAgentType).toBe("multimodal");
      expect(metrics.startTime).toBeDefined();
    });

    it("should generate Prometheus metrics", async () => {
      await agentManager.initialize();

      const prometheusMetrics = agentManager.getPrometheusMetrics();

      expect(prometheusMetrics).toContain("# HELP agent_initializations_total");
      expect(prometheusMetrics).toContain(
        "# TYPE agent_initializations_total counter"
      );
      expect(prometheusMetrics).toContain("agent_initializations_total 1");
    });
  });

  describe("Participant Handling", () => {
    it("should handle participant with multimodal agent", async () => {
      await agentManager.initialize();

      const mockContext = testUtils.generateMockContext();
      const mockParticipant = testUtils.generateMockParticipant();

      const result = await agentManager.handleParticipant(
        mockContext,
        mockParticipant
      );

      expect(result).toBeDefined();
      expect(agentManager.metrics.getMetrics().connectionsAttempted).toBe(1);
    });

    it("should fallback to direct STT when multimodal fails", async () => {
      await agentManager.initialize();

      // Mock multimodal agent to fail
      agentManager.primaryAgent.start = vi
        .fn()
        .mockRejectedValue(new Error("Multimodal failed"));

      const mockContext = testUtils.generateMockContext();
      const mockParticipant = testUtils.generateMockParticipant();

      await agentManager.handleParticipant(mockContext, mockParticipant);

      expect(agentManager.currentAgentType).toBe("direct-stt");
      expect(agentManager.status.fallbacksUsed).toBe(1);
    });
  });

  describe("Error Handling", () => {
    it("should handle initialization errors gracefully", async () => {
      // Mock all strategies to fail
      const originalStrategies = agentManager.agentStrategies;
      agentManager.agentStrategies = [
        {
          name: "failing-strategy",
          requirements: ["DEEPGRAM_API_KEY"],
          fallbackOnError: false,
        },
      ];

      // Mock the strategy to throw an error
      agentManager.initializeWithStrategy = vi
        .fn()
        .mockRejectedValue(new Error("Strategy failed"));

      const result = await agentManager.initialize();

      expect(result).toBe(false);
      expect(agentManager.status.status).toBe("error");
      expect(agentManager.status.errorDetails).toBeDefined();

      // Restore original strategies
      agentManager.agentStrategies = originalStrategies;
    });

    it("should record errors in metrics", async () => {
      // Force an error during initialization
      mockProcess.env = {}; // No API keys

      await agentManager.initialize();

      const metrics = agentManager.getMetrics();
      expect(metrics.agentInitializationFailures).toBe(1);
      expect(metrics.errorsTotal).toBeGreaterThan(0);
    });
  });

  describe("Performance", () => {
    it("should initialize within performance threshold", async () => {
      const start = performance.now();
      await agentManager.initialize();
      const duration = performance.now() - start;

      expect(duration).toBeLessThan(TEST_CONFIG.performanceThreshold);
    });

    it("should handle multiple rapid initializations", async () => {
      const results = await testUtils.runConcurrentTests(async (i) => {
        const manager = new EnhancedAgentManager();
        vi.stubGlobal("process", { env: testUtils.createMockEnvironment() });
        return await manager.initialize();
      }, 5);

      expect(results.successRate).toBeGreaterThan(0.8); // At least 80% success rate
    });
  });
});
