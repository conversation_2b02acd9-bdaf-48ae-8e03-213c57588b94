/**
 * Unit Tests for MetricsCollector
 * Tests the metrics collection and reporting functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { enableConsoleMocks, restoreConsole, TEST_CONFIG } from "../setup.js";

// Import the component to test
import { MetricsCollector } from "../../src/enhanced-agent-v2.js";

describe("MetricsCollector Unit Tests", () => {
  let metrics;

  beforeEach(() => {
    enableConsoleMocks();
    metrics = new MetricsCollector();

    // Stop the periodic collection for tests
    if (metrics.periodicInterval) {
      clearInterval(metrics.periodicInterval);
    }
  });

  afterEach(() => {
    restoreConsole();
    if (metrics.periodicInterval) {
      clearInterval(metrics.periodicInterval);
    }
  });

  describe("Initialization", () => {
    it("should initialize with default metrics", () => {
      const initialMetrics = metrics.getMetrics();

      expect(initialMetrics.agentInitializations).toBe(0);
      expect(initialMetrics.connectionsAttempted).toBe(0);
      expect(initialMetrics.transcriptionsProcessed).toBe(0);
      expect(initialMetrics.errorsTotal).toBe(0);
      expect(initialMetrics.startTime).toBeDefined();
      expect(initialMetrics.lastActivity).toBeDefined();
    });

    it("should have all required metric categories", () => {
      const metricsData = metrics.getMetrics();

      // Agent metrics
      expect(metricsData).toHaveProperty("agentInitializations");
      expect(metricsData).toHaveProperty("agentInitializationFailures");
      expect(metricsData).toHaveProperty("agentFallbacks");
      expect(metricsData).toHaveProperty("currentAgentType");

      // Connection metrics
      expect(metricsData).toHaveProperty("connectionsAttempted");
      expect(metricsData).toHaveProperty("connectionsSuccessful");
      expect(metricsData).toHaveProperty("connectionsFailed");
      expect(metricsData).toHaveProperty("averageConnectionTime");

      // Transcription metrics
      expect(metricsData).toHaveProperty("transcriptionsProcessed");
      expect(metricsData).toHaveProperty("interimResults");
      expect(metricsData).toHaveProperty("finalResults");
      expect(metricsData).toHaveProperty("averageConfidence");

      // Error metrics
      expect(metricsData).toHaveProperty("errorsTotal");
      expect(metricsData).toHaveProperty("errorsByType");
      expect(metricsData).toHaveProperty("errorsBySeverity");
    });
  });

  describe("Counter Operations", () => {
    it("should increment counters correctly", () => {
      metrics.incrementCounter("agentInitializations");
      metrics.incrementCounter("connectionsAttempted", 3);

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.agentInitializations).toBe(1);
      expect(currentMetrics.connectionsAttempted).toBe(3);
    });

    it("should handle invalid counter names gracefully", () => {
      metrics.incrementCounter("nonExistentCounter");

      // Should not throw error and metrics should remain unchanged
      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.agentInitializations).toBe(0);
    });

    it("should increment by default value of 1", () => {
      metrics.incrementCounter("agentInitializations");
      metrics.incrementCounter("agentInitializations");

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.agentInitializations).toBe(2);
    });
  });

  describe("Gauge Operations", () => {
    it("should update gauges correctly", () => {
      metrics.updateGauge("memoryUsage", 1024);
      metrics.updateGauge("currentAgentType", "multimodal");

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.memoryUsage).toBe(1024);
      expect(currentMetrics.currentAgentType).toBe("multimodal");
    });

    it("should overwrite previous gauge values", () => {
      metrics.updateGauge("memoryUsage", 1024);
      metrics.updateGauge("memoryUsage", 2048);

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.memoryUsage).toBe(2048);
    });

    it("should handle invalid gauge names gracefully", () => {
      metrics.updateGauge("nonExistentGauge", 100);

      // Should not throw error
      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.memoryUsage).toBe(0);
    });
  });

  describe("Error Recording", () => {
    it("should record errors with type and severity", () => {
      metrics.recordError("CONNECTION_FAILED", "high");
      metrics.recordError("CONNECTION_FAILED", "medium");
      metrics.recordError("AUDIO_ERROR", "low");

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.errorsTotal).toBe(3);
      expect(currentMetrics.errorsByType["CONNECTION_FAILED"]).toBe(2);
      expect(currentMetrics.errorsByType["AUDIO_ERROR"]).toBe(1);
      expect(currentMetrics.errorsBySeverity["high"]).toBe(1);
      expect(currentMetrics.errorsBySeverity["medium"]).toBe(1);
      expect(currentMetrics.errorsBySeverity["low"]).toBe(1);
    });

    it("should handle multiple errors of same type", () => {
      for (let i = 0; i < 5; i++) {
        metrics.recordError("CONNECTION_FAILED", "high");
      }

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.errorsTotal).toBe(5);
      expect(currentMetrics.errorsByType["CONNECTION_FAILED"]).toBe(5);
      expect(currentMetrics.errorsBySeverity["high"]).toBe(5);
    });
  });

  describe("Transcription Recording", () => {
    it("should record transcriptions and calculate average confidence", () => {
      metrics.recordTranscription(0.9, true);
      metrics.recordTranscription(0.8, false);
      metrics.recordTranscription(0.7, true);

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.transcriptionsProcessed).toBe(3);
      expect(currentMetrics.finalResults).toBe(2);
      expect(currentMetrics.interimResults).toBe(1);
      expect(currentMetrics.averageConfidence).toBeCloseTo(0.8, 1);
    });

    it("should handle edge cases in confidence calculation", () => {
      metrics.recordTranscription(1.0, true);

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.averageConfidence).toBe(1.0);

      metrics.recordTranscription(0.0, false);
      const updatedMetrics = metrics.getMetrics();
      expect(updatedMetrics.averageConfidence).toBe(0.5);
    });

    it("should distinguish between interim and final results", () => {
      metrics.recordTranscription(0.8, false); // interim
      metrics.recordTranscription(0.9, false); // interim
      metrics.recordTranscription(0.95, true); // final

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.transcriptionsProcessed).toBe(3);
      expect(currentMetrics.interimResults).toBe(2);
      expect(currentMetrics.finalResults).toBe(1);
    });
  });

  describe("Connection Recording", () => {
    it("should record connections and calculate average time", () => {
      metrics.recordConnection(true, 100);
      metrics.recordConnection(true, 200);
      metrics.recordConnection(false, 0);

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.connectionsAttempted).toBe(3);
      expect(currentMetrics.connectionsSuccessful).toBe(2);
      expect(currentMetrics.connectionsFailed).toBe(1);
      expect(currentMetrics.averageConnectionTime).toBe(150);
    });

    it("should handle failed connections correctly", () => {
      metrics.recordConnection(false, 0);
      metrics.recordConnection(false, 0);

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.connectionsAttempted).toBe(2);
      expect(currentMetrics.connectionsSuccessful).toBe(0);
      expect(currentMetrics.connectionsFailed).toBe(2);
      expect(currentMetrics.averageConnectionTime).toBe(0);
    });

    it("should calculate average time only for successful connections", () => {
      metrics.recordConnection(true, 100);
      metrics.recordConnection(false, 0); // Should not affect average
      metrics.recordConnection(true, 300);

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.averageConnectionTime).toBe(200);
    });
  });

  describe("Prometheus Metrics Export", () => {
    it("should generate Prometheus metrics format", () => {
      metrics.incrementCounter("agentInitializations", 2);
      metrics.recordConnection(true, 100);
      metrics.recordTranscription(0.9, true);

      const prometheusMetrics = metrics.getPrometheusMetrics();

      expect(prometheusMetrics).toContain("# HELP agent_initializations_total");
      expect(prometheusMetrics).toContain(
        "# TYPE agent_initializations_total counter"
      );
      expect(prometheusMetrics).toContain("agent_initializations_total 2");

      expect(prometheusMetrics).toContain(
        'connections_total{status="attempted"} 1'
      );
      expect(prometheusMetrics).toContain(
        'connections_total{status="successful"} 1'
      );

      expect(prometheusMetrics).toContain(
        'transcriptions_total{type="final"} 1'
      );
      expect(prometheusMetrics).toContain("agent_uptime_seconds");
      expect(prometheusMetrics).toContain("memory_usage_bytes");
    });

    it("should include all metric types in Prometheus format", () => {
      const prometheusMetrics = metrics.getPrometheusMetrics();

      // Check for counter metrics
      expect(prometheusMetrics).toContain(
        "# TYPE agent_initializations_total counter"
      );
      expect(prometheusMetrics).toContain("# TYPE connections_total counter");
      expect(prometheusMetrics).toContain(
        "# TYPE transcriptions_total counter"
      );

      // Check for gauge metrics
      expect(prometheusMetrics).toContain("# TYPE agent_uptime_seconds gauge");
      expect(prometheusMetrics).toContain("# TYPE memory_usage_bytes gauge");
    });

    it("should format metrics with proper labels", () => {
      metrics.recordConnection(true, 100);
      metrics.recordConnection(false, 0);
      metrics.recordTranscription(0.9, true);
      metrics.recordTranscription(0.8, false);

      const prometheusMetrics = metrics.getPrometheusMetrics();

      expect(prometheusMetrics).toContain(
        'connections_total{status="attempted"}'
      );
      expect(prometheusMetrics).toContain(
        'connections_total{status="successful"}'
      );
      expect(prometheusMetrics).toContain('connections_total{status="failed"}');
      expect(prometheusMetrics).toContain(
        'transcriptions_total{type="interim"}'
      );
      expect(prometheusMetrics).toContain('transcriptions_total{type="final"}');
    });
  });

  describe("System Metrics Collection", () => {
    it("should collect system metrics", () => {
      metrics.collectSystemMetrics();

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.uptime).toBeGreaterThan(0);
      expect(currentMetrics.lastActivity).toBeDefined();

      if (process.memoryUsage) {
        expect(currentMetrics.memoryUsage).toBeGreaterThan(0);
      }
    });

    it("should update uptime correctly", async () => {
      const initialMetrics = metrics.getMetrics();
      const initialUptime = initialMetrics.uptime;

      // Wait a bit
      await new Promise((resolve) => setTimeout(resolve, 10));

      metrics.collectSystemMetrics();
      const updatedMetrics = metrics.getMetrics();

      expect(updatedMetrics.uptime).toBeGreaterThan(initialUptime);
    });
  });

  describe("Performance", () => {
    it("should handle high-frequency metric updates", () => {
      const start = performance.now();

      for (let i = 0; i < 1000; i++) {
        metrics.incrementCounter("agentInitializations");
        metrics.recordTranscription(0.9, i % 2 === 0);
        metrics.recordError("TEST_ERROR", "low");
      }

      const duration = performance.now() - start;
      expect(duration).toBeLessThan(100); // Should complete in under 100ms

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.agentInitializations).toBe(1000);
      expect(currentMetrics.transcriptionsProcessed).toBe(1000);
      expect(currentMetrics.errorsTotal).toBe(1000);
    });

    it("should maintain accuracy under concurrent access", async () => {
      const promises = Array.from({ length: 10 }, async (_, i) => {
        for (let j = 0; j < 100; j++) {
          metrics.incrementCounter("agentInitializations");
          metrics.recordTranscription(0.9, true);
        }
      });

      await Promise.all(promises);

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.agentInitializations).toBe(1000);
      expect(currentMetrics.transcriptionsProcessed).toBe(1000);
    });
  });

  describe("Edge Cases", () => {
    it("should handle zero values correctly", () => {
      metrics.recordTranscription(0, true);
      metrics.recordConnection(true, 0);

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.averageConfidence).toBe(0);
      expect(currentMetrics.averageConnectionTime).toBe(0);
    });

    it("should handle very large numbers", () => {
      const largeNumber = Number.MAX_SAFE_INTEGER - 1;
      metrics.updateGauge("memoryUsage", largeNumber);

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.memoryUsage).toBe(largeNumber);
    });

    it("should handle negative values appropriately", () => {
      // Negative values should be handled gracefully
      metrics.recordConnection(true, -100);

      const currentMetrics = metrics.getMetrics();
      expect(currentMetrics.averageConnectionTime).toBe(-100);
    });
  });
});
