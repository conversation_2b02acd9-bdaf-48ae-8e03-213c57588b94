/**
 * Comprehensive Error Handler Tests
 * Tests the error handling system with various scenarios
 */

import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import {
  Error<PERSON>andler,
  ErrorTypes,
  ErrorSeverity,
  TranscriptionError,
  createError,
  handleError,
  createComponentErrorHandler,
} from "../src/error-handler.js";

describe("TranscriptionError", () => {
  it("should create error with all properties", () => {
    const error = new TranscriptionError(
      ErrorTypes.CONNECTION_FAILED,
      "Connection failed",
      {
        severity: ErrorSeverity.HIGH,
        context: { component: "test" },
        userMessage: "Custom user message",
        suggestions: ["Try again"],
      }
    );

    expect(error.type).toBe(ErrorTypes.CONNECTION_FAILED);
    expect(error.message).toBe("Connection failed");
    expect(error.severity).toBe(ErrorSeverity.HIGH);
    expect(error.userMessage).toBe("Custom user message");
    expect(error.suggestions).toEqual(["Try again"]);
    expect(error.retryable).toBe(true);
    expect(error.context).toEqual({ component: "test" });
  });

  it("should generate appropriate user messages", () => {
    const connectionError = new TranscriptionError(
      ErrorTypes.CONNECTION_FAILED,
      "Test"
    );
    expect(connectionError.userMessage).toBe(
      "无法连接到服务器，请检查网络连接"
    );

    const micError = new TranscriptionError(
      ErrorTypes.MICROPHONE_ACCESS_DENIED,
      "Test"
    );
    expect(micError.userMessage).toBe("需要麦克风权限才能进行转录");

    const unknownError = new TranscriptionError(
      ErrorTypes.UNKNOWN_ERROR,
      "Test"
    );
    expect(unknownError.userMessage).toBe("发生了未知错误，请稍后重试");
  });

  it("should generate appropriate suggestions", () => {
    const connectionError = new TranscriptionError(
      ErrorTypes.CONNECTION_FAILED,
      "Test"
    );
    expect(connectionError.suggestions).toContain("检查网络连接是否正常");

    const micError = new TranscriptionError(
      ErrorTypes.MICROPHONE_ACCESS_DENIED,
      "Test"
    );
    expect(micError.suggestions).toContain("点击浏览器地址栏的麦克风图标");
  });

  it("should serialize to JSON correctly", () => {
    const error = new TranscriptionError(
      ErrorTypes.CONNECTION_FAILED,
      "Test error"
    );
    const json = error.toJSON();

    expect(json.name).toBe("TranscriptionError");
    expect(json.type).toBe(ErrorTypes.CONNECTION_FAILED);
    expect(json.message).toBe("Test error");
    expect(json.severity).toBe(ErrorSeverity.MEDIUM);
    expect(json.retryable).toBe(true);
    expect(json).toHaveProperty("timestamp");
    expect(json).toHaveProperty("userMessage");
    expect(json).toHaveProperty("suggestions");
  });
});

describe("ErrorHandler", () => {
  let errorHandler;
  let mockConsole;

  beforeEach(() => {
    errorHandler = new ErrorHandler({
      maxRetries: 2,
      baseDelay: 100,
      maxDelay: 1000,
      enableLogging: false, // Disable logging for tests
    });

    // Mock console methods
    mockConsole = {
      log: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    };
    vi.stubGlobal("console", mockConsole);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("error normalization", () => {
    it("should normalize string errors", () => {
      const normalized = errorHandler.normalizeError("Simple error message");

      expect(normalized).toBeInstanceOf(TranscriptionError);
      expect(normalized.type).toBe(ErrorTypes.UNKNOWN_ERROR);
      expect(normalized.message).toBe("Simple error message");
    });

    it("should normalize generic Error objects", () => {
      const genericError = new Error("Generic error");
      const normalized = errorHandler.normalizeError(genericError);

      expect(normalized).toBeInstanceOf(TranscriptionError);
      expect(normalized.message).toBe("Generic error");
      expect(normalized.originalError).toBe(genericError);
    });

    it("should pass through TranscriptionError unchanged", () => {
      const transcriptionError = new TranscriptionError(
        ErrorTypes.CONNECTION_FAILED,
        "Test"
      );
      const normalized = errorHandler.normalizeError(transcriptionError);

      expect(normalized).toBe(transcriptionError);
    });

    it("should detect error types from messages", () => {
      const connectionError = errorHandler.normalizeError(
        new Error("Connection timeout occurred")
      );
      expect(connectionError.type).toBe(ErrorTypes.CONNECTION_TIMEOUT);

      const authError = errorHandler.normalizeError(new Error("Token expired"));
      expect(authError.type).toBe(ErrorTypes.TOKEN_EXPIRED);

      const micError = errorHandler.normalizeError(
        new Error("Microphone permission denied")
      );
      expect(micError.type).toBe(ErrorTypes.MICROPHONE_ACCESS_DENIED);
    });
  });

  describe("error handling without retry", () => {
    it("should handle error and emit event", async () => {
      const emitSpy = vi.spyOn(errorHandler, "emit");
      const testError = new Error("Test error");

      const result = await errorHandler.handleError(testError, {
        component: "test",
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(TranscriptionError);
      expect(emitSpy).toHaveBeenCalledWith(
        "error",
        expect.any(TranscriptionError)
      );
      expect(errorHandler.errorHistory).toHaveLength(1);
    });

    it("should add errors to history", async () => {
      const error1 = new Error("Error 1");
      const error2 = new Error("Error 2");

      await errorHandler.handleError(error1);
      await errorHandler.handleError(error2);

      expect(errorHandler.errorHistory).toHaveLength(2);
      expect(errorHandler.errorHistory[0].error.message).toBe("Error 2"); // Most recent first
      expect(errorHandler.errorHistory[1].error.message).toBe("Error 1");
    });
  });

  describe("retry mechanism", () => {
    it("should retry failed operations", async () => {
      let attemptCount = 0;
      const retryFunction = vi.fn().mockImplementation(() => {
        attemptCount++;
        if (attemptCount < 2) {
          throw new Error(`Attempt ${attemptCount} failed`);
        }
        return "success";
      });

      const emitSpy = vi.spyOn(errorHandler, "emit");
      const testError = new TranscriptionError(
        ErrorTypes.CONNECTION_FAILED,
        "Initial error"
      );

      const result = await errorHandler.handleError(
        testError,
        { component: "test" },
        retryFunction
      );

      expect(result.success).toBe(true);
      expect(result.result).toBe("success");
      expect(result.retriesUsed).toBe(1);
      expect(retryFunction).toHaveBeenCalledTimes(2);
      expect(emitSpy).toHaveBeenCalledWith("retrySuccess", expect.any(Object));
    });

    it("should exhaust retries and fail", async () => {
      const retryFunction = vi
        .fn()
        .mockRejectedValue(new Error("Always fails"));
      const emitSpy = vi.spyOn(errorHandler, "emit");
      const testError = new TranscriptionError(
        ErrorTypes.CONNECTION_FAILED,
        "Initial error"
      );

      const result = await errorHandler.handleError(
        testError,
        { component: "test" },
        retryFunction
      );

      expect(result.success).toBe(false);
      expect(result.retriesExhausted).toBe(true);
      expect(retryFunction).toHaveBeenCalledTimes(3); // Initial + 2 retries
      expect(emitSpy).toHaveBeenCalledWith(
        "retryExhausted",
        expect.any(Object)
      );
    });

    it("should not retry non-retryable errors", async () => {
      const retryFunction = vi.fn();
      const nonRetryableError = new TranscriptionError(
        ErrorTypes.CONFIG_MISSING,
        "Config error",
        { retryable: false }
      );

      const result = await errorHandler.handleError(
        nonRetryableError,
        {},
        retryFunction
      );

      expect(result.success).toBe(false);
      expect(retryFunction).not.toHaveBeenCalled();
    });

    it("should use exponential backoff", async () => {
      const delays = [];
      const originalSleep = errorHandler.sleep;
      errorHandler.sleep = vi.fn().mockImplementation((delay) => {
        delays.push(delay);
        return Promise.resolve();
      });

      const retryFunction = vi
        .fn()
        .mockRejectedValue(new Error("Always fails"));
      const testError = new TranscriptionError(
        ErrorTypes.CONNECTION_FAILED,
        "Test"
      );

      await errorHandler.handleError(testError, {}, retryFunction);

      expect(delays).toHaveLength(2); // 2 retries
      expect(delays[1]).toBeGreaterThan(delays[0]); // Exponential backoff

      errorHandler.sleep = originalSleep;
    });
  });

  describe("error statistics", () => {
    it("should provide error statistics", async () => {
      await errorHandler.handleError(
        new TranscriptionError(ErrorTypes.CONNECTION_FAILED, "Error 1", {
          severity: ErrorSeverity.HIGH,
        })
      );
      await errorHandler.handleError(
        new TranscriptionError(ErrorTypes.AUDIO_TRACK_FAILED, "Error 2", {
          severity: ErrorSeverity.MEDIUM,
        })
      );
      await errorHandler.handleError(
        new TranscriptionError(ErrorTypes.CONNECTION_FAILED, "Error 3", {
          severity: ErrorSeverity.HIGH,
        })
      );

      const stats = errorHandler.getErrorStats();

      expect(stats.total).toBe(3);
      expect(stats.bySeverity[ErrorSeverity.HIGH]).toBe(2);
      expect(stats.bySeverity[ErrorSeverity.MEDIUM]).toBe(1);
      expect(stats.byType[ErrorTypes.CONNECTION_FAILED]).toBe(2);
      expect(stats.byType[ErrorTypes.AUDIO_TRACK_FAILED]).toBe(1);
      expect(stats.recent).toHaveLength(3);
    });

    it("should clear history and retry attempts", () => {
      errorHandler.errorHistory = [{ test: "data" }];
      errorHandler.retryAttempts.set("test", 1);

      errorHandler.clearHistory();

      expect(errorHandler.errorHistory).toHaveLength(0);
      expect(errorHandler.retryAttempts.size).toBe(0);
    });
  });

  describe("component error handler", () => {
    it("should create component-specific error handler", async () => {
      const componentHandler =
        errorHandler.createComponentHandler("TestComponent");
      const testError = new Error("Component error");

      const result = await componentHandler.handle(testError, {
        operation: "test",
      });

      expect(result.success).toBe(false);
      expect(result.error.context.component).toBe("TestComponent");
      expect(result.error.context.operation).toBe("test");
    });

    it("should handle retry with component context", async () => {
      const componentHandler =
        errorHandler.createComponentHandler("TestComponent");
      const retryFunction = vi.fn().mockResolvedValue("success");
      const testError = new Error("Component error");

      const result = await componentHandler.handleWithRetry(
        testError,
        retryFunction,
        { operation: "test" }
      );

      expect(result.success).toBe(true);
      expect(result.result).toBe("success");
    });
  });
});

describe("convenience functions", () => {
  it("should create error with createError", () => {
    const error = createError(ErrorTypes.CONNECTION_FAILED, "Test message", {
      severity: ErrorSeverity.HIGH,
    });

    expect(error).toBeInstanceOf(TranscriptionError);
    expect(error.type).toBe(ErrorTypes.CONNECTION_FAILED);
    expect(error.message).toBe("Test message");
    expect(error.severity).toBe(ErrorSeverity.HIGH);
  });

  it("should handle error with handleError", async () => {
    const testError = new Error("Test error");
    const result = await handleError(testError, { component: "test" });

    expect(result.success).toBe(false);
    expect(result.error).toBeInstanceOf(TranscriptionError);
  });

  it("should create component error handler", async () => {
    const componentHandler = createComponentErrorHandler("TestComponent");
    const testError = new Error("Test error");

    const result = await componentHandler.handle(testError);

    expect(result.success).toBe(false);
    expect(result.error.context.component).toBe("TestComponent");
  });
});

describe("error severity determination", () => {
  let errorHandler;

  beforeEach(() => {
    errorHandler = new ErrorHandler({ enableLogging: false });
  });

  it("should determine critical severity", () => {
    const severity = errorHandler.determineSeverity(
      ErrorTypes.AGENT_CRASHED,
      {}
    );
    expect(severity).toBe(ErrorSeverity.CRITICAL);
  });

  it("should determine high severity", () => {
    const severity = errorHandler.determineSeverity(
      ErrorTypes.CONNECTION_FAILED,
      {}
    );
    expect(severity).toBe(ErrorSeverity.HIGH);
  });

  it("should determine medium severity", () => {
    const severity = errorHandler.determineSeverity(
      ErrorTypes.CONNECTION_TIMEOUT,
      {}
    );
    expect(severity).toBe(ErrorSeverity.MEDIUM);
  });

  it("should default to low severity", () => {
    const severity = errorHandler.determineSeverity("UNKNOWN_TYPE", {});
    expect(severity).toBe(ErrorSeverity.LOW);
  });
});

describe("retry key generation", () => {
  let errorHandler;

  beforeEach(() => {
    errorHandler = new ErrorHandler({ enableLogging: false });
  });

  it("should generate consistent retry keys", () => {
    const error = new TranscriptionError(ErrorTypes.CONNECTION_FAILED, "Test");
    const context = { component: "TestComponent", operation: "connect" };

    const key1 = errorHandler.generateRetryKey(error, context);
    const key2 = errorHandler.generateRetryKey(error, context);

    expect(key1).toBe(key2);
    expect(key1).toBe("CONNECTION_FAILED_TestComponent_connect");
  });

  it("should handle missing context properties", () => {
    const error = new TranscriptionError(ErrorTypes.CONNECTION_FAILED, "Test");
    const context = {};

    const key = errorHandler.generateRetryKey(error, context);
    expect(key).toBe("CONNECTION_FAILED_unknown_unknown");
  });
});
