/**
 * Integration test for error handling system
 * Tests the complete error handling flow
 */

import { describe, it, expect, beforeEach } from "vitest";
import {
  Error<PERSON>andler,
  ErrorTypes,
  ErrorSeverity,
  TranscriptionError,
  createError,
  handleError,
} from "../src/error-handler.js";

describe("Error Handling Integration", () => {
  let errorHandler;

  beforeEach(() => {
    errorHandler = new ErrorHandler({
      maxRetries: 2,
      baseDelay: 10, // Fast for testing
      maxDelay: 100,
      enableLogging: false,
    });
  });

  it("should create and handle a complete error flow", async () => {
    // Create a connection error
    const connectionError = createError(
      ErrorTypes.CONNECTION_FAILED,
      "Failed to connect to server",
      {
        severity: ErrorSeverity.HIGH,
        context: { server: "localhost:3000" },
      }
    );

    expect(connectionError).toBeInstanceOf(TranscriptionError);
    expect(connectionError.type).toBe(ErrorTypes.CONNECTION_FAILED);
    expect(connectionError.userMessage).toBe(
      "无法连接到服务器，请检查网络连接"
    );
    expect(connectionError.suggestions).toContain("检查网络连接是否正常");
  });

  it("should handle microphone permission errors with proper suggestions", () => {
    const micError = createError(
      ErrorTypes.MICROPHONE_ACCESS_DENIED,
      "Permission denied for microphone",
      { severity: ErrorSeverity.HIGH }
    );

    expect(micError.userMessage).toBe("需要麦克风权限才能进行转录");
    expect(micError.suggestions).toContain("点击浏览器地址栏的麦克风图标");
    expect(micError.suggestions).toContain("选择'允许'麦克风权限");
  });

  it("should handle STT quota errors with appropriate guidance", () => {
    const quotaError = createError(
      ErrorTypes.STT_QUOTA_EXCEEDED,
      "API quota exceeded",
      { severity: ErrorSeverity.MEDIUM }
    );

    expect(quotaError.userMessage).toBe("语音识别配额已用完，请稍后重试");
    expect(quotaError.suggestions).toContain("等待配额重置（通常在下一小时）");
    expect(quotaError.suggestions).toContain("联系管理员增加配额");
  });

  it("should handle configuration errors with setup guidance", () => {
    const configError = createError(
      ErrorTypes.CONFIG_MISSING,
      "Missing API key configuration",
      { severity: ErrorSeverity.CRITICAL }
    );

    expect(configError.userMessage).toBe("系统配置不完整，请联系管理员");
    expect(configError.suggestions).toContain("检查环境变量配置");
    expect(configError.suggestions).toContain("确认 API 密钥设置正确");
  });

  it("should provide error statistics", async () => {
    // Add some errors to history
    await errorHandler.handleError(
      createError(ErrorTypes.CONNECTION_FAILED, "Error 1")
    );
    await errorHandler.handleError(
      createError(ErrorTypes.AUDIO_TRACK_FAILED, "Error 2")
    );
    await errorHandler.handleError(
      createError(ErrorTypes.CONNECTION_FAILED, "Error 3")
    );

    const stats = errorHandler.getErrorStats();

    expect(stats.total).toBe(3);
    expect(stats.byType[ErrorTypes.CONNECTION_FAILED]).toBe(2);
    expect(stats.byType[ErrorTypes.AUDIO_TRACK_FAILED]).toBe(1);
    expect(stats.recent).toHaveLength(3);
  });

  it("should serialize errors to JSON properly", () => {
    const error = createError(
      ErrorTypes.AGENT_INIT_FAILED,
      "Agent initialization failed",
      {
        severity: ErrorSeverity.CRITICAL,
        context: { agentType: "multimodal" },
      }
    );

    const json = error.toJSON();

    expect(json).toHaveProperty("name", "TranscriptionError");
    expect(json).toHaveProperty("type", ErrorTypes.AGENT_INIT_FAILED);
    expect(json).toHaveProperty("message", "Agent initialization failed");
    expect(json).toHaveProperty("userMessage");
    expect(json).toHaveProperty("severity", ErrorSeverity.CRITICAL);
    expect(json).toHaveProperty("suggestions");
    expect(json).toHaveProperty("timestamp");
    expect(json).toHaveProperty("context");
    expect(json.context).toHaveProperty("agentType", "multimodal");
  });

  it("should detect error types from error messages", () => {
    const handler = new ErrorHandler({ enableLogging: false });

    // Test connection error detection
    const connectionError = handler.normalizeError(
      new Error("Connection timeout occurred")
    );
    expect(connectionError.type).toBe(ErrorTypes.CONNECTION_TIMEOUT);

    // Test auth error detection
    const authError = handler.normalizeError(new Error("Token expired"));
    expect(authError.type).toBe(ErrorTypes.TOKEN_EXPIRED);

    // Test microphone error detection
    const micError = handler.normalizeError(
      new Error("Microphone permission denied")
    );
    expect(micError.type).toBe(ErrorTypes.MICROPHONE_ACCESS_DENIED);

    // Test STT error detection
    const sttError = handler.normalizeError(
      new Error("Deepgram quota exceeded")
    );
    expect(sttError.type).toBe(ErrorTypes.STT_QUOTA_EXCEEDED);
  });

  it("should determine appropriate error severity", () => {
    const handler = new ErrorHandler({ enableLogging: false });

    expect(handler.determineSeverity(ErrorTypes.AGENT_CRASHED, {})).toBe(
      ErrorSeverity.CRITICAL
    );
    expect(handler.determineSeverity(ErrorTypes.CONNECTION_FAILED, {})).toBe(
      ErrorSeverity.HIGH
    );
    expect(handler.determineSeverity(ErrorTypes.CONNECTION_TIMEOUT, {})).toBe(
      ErrorSeverity.MEDIUM
    );
    expect(handler.determineSeverity("UNKNOWN_TYPE", {})).toBe(
      ErrorSeverity.LOW
    );
  });

  it("should generate consistent retry keys", () => {
    const handler = new ErrorHandler({ enableLogging: false });
    const error = createError(ErrorTypes.CONNECTION_FAILED, "Test");
    const context = { component: "TestComponent", operation: "connect" };

    const key1 = handler.generateRetryKey(error, context);
    const key2 = handler.generateRetryKey(error, context);

    expect(key1).toBe(key2);
    expect(key1).toBe("CONNECTION_FAILED_TestComponent_connect");
  });

  it("should calculate exponential backoff delays", () => {
    const handler = new ErrorHandler({
      baseDelay: 100,
      maxDelay: 1000,
      backoffMultiplier: 2,
      enableLogging: false,
    });

    const delay1 = handler.calculateBackoffDelay(0);
    const delay2 = handler.calculateBackoffDelay(1);
    const delay3 = handler.calculateBackoffDelay(2);

    expect(delay1).toBeGreaterThanOrEqual(100);
    expect(delay1).toBeLessThanOrEqual(110); // With jitter
    expect(delay2).toBeGreaterThanOrEqual(200);
    expect(delay2).toBeLessThanOrEqual(220); // With jitter
    expect(delay3).toBeGreaterThanOrEqual(400);
    expect(delay3).toBeLessThanOrEqual(440); // With jitter
  });
});
