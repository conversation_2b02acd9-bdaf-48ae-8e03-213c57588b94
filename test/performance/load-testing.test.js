/**
 * Performance and Load Testing Suite
 * Tests system behavior under various load conditions
 */

import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import {
  enableConsoleMocks,
  restoreConsole,
  testUtils,
  performanceMonitor,
  TEST_CONFIG,
} from "../setup.js";

// Import components for performance testing
import {
  EnhancedAgentManager,
  MetricsCollector,
} from "../../src/enhanced-agent-v2.js";
import { AudioStreamProcessor } from "../../src/audio-stream-processor.js";

describe("Performance and Load Testing", () => {
  let agentManager;
  let metricsCollector;

  beforeEach(async () => {
    enableConsoleMocks();
    performanceMonitor.reset();

    // Setup test environment
    vi.stubGlobal("process", { env: testUtils.createMockEnvironment() });

    agentManager = new EnhancedAgentManager();
    metricsCollector = new MetricsCollector();

    await agentManager.initialize();
  });

  afterEach(() => {
    restoreConsole();
    vi.restoreAllMocks();
  });

  describe("Agent Initialization Performance", () => {
    it("should initialize within acceptable time limits", async () => {
      const initializationTimes = [];

      for (let i = 0; i < 10; i++) {
        const manager = new EnhancedAgentManager();
        vi.stubGlobal("process", { env: testUtils.createMockEnvironment() });

        const start = performance.now();
        await manager.initialize();
        const duration = performance.now() - start;

        initializationTimes.push(duration);
        expect(duration).toBeLessThan(TEST_CONFIG.performanceThreshold);
      }

      const averageTime =
        initializationTimes.reduce((a, b) => a + b, 0) /
        initializationTimes.length;
      const maxTime = Math.max(...initializationTimes);
      const minTime = Math.min(...initializationTimes);

      console.log(`Initialization Performance:
        Average: ${averageTime.toFixed(2)}ms
        Min: ${minTime.toFixed(2)}ms
        Max: ${maxTime.toFixed(2)}ms
        Threshold: ${TEST_CONFIG.performanceThreshold}ms`);

      expect(averageTime).toBeLessThan(TEST_CONFIG.performanceThreshold * 0.5);
      expect(maxTime).toBeLessThan(TEST_CONFIG.performanceThreshold);
    });

    it("should handle concurrent initializations efficiently", async () => {
      const concurrentCount = 20;

      performanceMonitor.startTimer("concurrent-init");

      const results = await testUtils.runConcurrentTests(async (i) => {
        const manager = new EnhancedAgentManager();
        vi.stubGlobal("process", { env: testUtils.createMockEnvironment() });

        const start = performance.now();
        const success = await manager.initialize();
        const duration = performance.now() - start;

        return { success, duration, index: i };
      }, concurrentCount);

      const totalDuration = performanceMonitor.endTimer("concurrent-init");

      expect(results.successRate).toBeGreaterThan(0.9); // 90% success rate
      expect(totalDuration).toBeLessThan(TEST_CONFIG.performanceThreshold * 2);

      // Analyze individual durations
      const durations = results.results
        .filter((r) => !r.error)
        .map((r) => r.duration);

      const avgDuration =
        durations.reduce((a, b) => a + b, 0) / durations.length;
      expect(avgDuration).toBeLessThan(TEST_CONFIG.performanceThreshold);
    });
  });

  describe("Metrics Collection Performance", () => {
    it("should handle high-frequency metric updates efficiently", async () => {
      const updateCount = 10000;

      performanceMonitor.startTimer("metrics-updates");

      for (let i = 0; i < updateCount; i++) {
        metricsCollector.incrementCounter("agentInitializations");
        metricsCollector.recordTranscription(0.9, i % 2 === 0);
        metricsCollector.recordConnection(true, 100 + (i % 100));
        metricsCollector.recordError("TEST_ERROR", "low");

        if (i % 1000 === 0) {
          metricsCollector.collectSystemMetrics();
        }
      }

      const duration = performanceMonitor.endTimer("metrics-updates");

      expect(duration).toBeLessThan(1000); // Should complete in under 1 second

      // Verify metrics accuracy
      const metrics = metricsCollector.getMetrics();
      expect(metrics.agentInitializations).toBe(updateCount);
      expect(metrics.transcriptionsProcessed).toBe(updateCount);
      expect(metrics.errorsTotal).toBe(updateCount);

      console.log(`Metrics Performance:
        Updates: ${updateCount}
        Duration: ${duration.toFixed(2)}ms
        Rate: ${((updateCount / duration) * 1000).toFixed(0)} updates/sec`);
    });

    it("should generate Prometheus metrics efficiently", async () => {
      // Populate metrics with substantial data
      for (let i = 0; i < 1000; i++) {
        metricsCollector.incrementCounter("agentInitializations");
        metricsCollector.recordTranscription(
          0.8 + Math.random() * 0.2,
          i % 3 === 0
        );
        metricsCollector.recordConnection(
          Math.random() > 0.1,
          50 + Math.random() * 200
        );
        metricsCollector.recordError(
          `ERROR_TYPE_${i % 5}`,
          ["low", "medium", "high"][i % 3]
        );
      }

      const generationTimes = [];

      for (let i = 0; i < 10; i++) {
        const start = performance.now();
        const prometheusMetrics = metricsCollector.getPrometheusMetrics();
        const duration = performance.now() - start;

        generationTimes.push(duration);
        expect(prometheusMetrics).toBeTruthy();
        expect(prometheusMetrics.length).toBeGreaterThan(0);
      }

      const avgGenerationTime =
        generationTimes.reduce((a, b) => a + b, 0) / generationTimes.length;
      expect(avgGenerationTime).toBeLessThan(100); // Should generate in under 100ms
    });
  });

  describe("Audio Processing Performance", () => {
    it("should handle multiple audio streams efficiently", async () => {
      const streamCount = 10;
      const mockSTT = {
        stream: vi.fn().mockReturnValue({
          input: { put: vi.fn() },
          [Symbol.asyncIterator]: async function* () {
            yield { text: "performance test", confidence: 0.9, isFinal: true };
          },
        }),
      };

      const audioProcessor = new AudioStreamProcessor(mockSTT);
      const mockContext = testUtils.generateMockContext();

      performanceMonitor.startTimer("audio-streams");

      const streamPromises = Array.from(
        { length: streamCount },
        async (_, i) => {
          const mockTrack = testUtils.generateMockTrack(`perf-track-${i}`);
          const mockParticipant = testUtils.generateMockParticipant(
            `perf-participant-${i}`
          );

          const start = performance.now();
          const success = await audioProcessor.connectTrack(
            mockTrack,
            mockContext,
            mockParticipant
          );
          const duration = performance.now() - start;

          return { success, duration, trackId: mockTrack.info.sid };
        }
      );

      const results = await Promise.all(streamPromises);
      const totalDuration = performanceMonitor.endTimer("audio-streams");

      const successfulStreams = results.filter((r) => r.success);
      expect(successfulStreams.length).toBe(streamCount);
      expect(totalDuration).toBeLessThan(
        TEST_CONFIG.performanceThreshold * streamCount * 0.1
      );

      // Analyze individual stream connection times
      const connectionTimes = results.map((r) => r.duration);
      const avgConnectionTime =
        connectionTimes.reduce((a, b) => a + b, 0) / connectionTimes.length;

      expect(avgConnectionTime).toBeLessThan(
        TEST_CONFIG.performanceThreshold * 0.5
      );
    });

    it("should maintain performance under connection failures", async () => {
      const mockSTT = {
        stream: vi.fn().mockReturnValue({
          input: {
            put: vi
              .fn()
              .mockRejectedValueOnce(new Error("First failure"))
              .mockRejectedValueOnce(new Error("Second failure"))
              .mockResolvedValue(true),
          },
          [Symbol.asyncIterator]: async function* () {
            yield { text: "retry test", confidence: 0.9, isFinal: true };
          },
        }),
      };

      const audioProcessor = new AudioStreamProcessor(mockSTT, {
        maxRetries: 3,
        baseDelay: 10, // Faster for testing
        maxDelay: 100,
      });

      const mockContext = testUtils.generateMockContext();
      const mockTrack = testUtils.generateMockTrack("retry-track");
      const mockParticipant =
        testUtils.generateMockParticipant("retry-participant");

      performanceMonitor.startTimer("retry-performance");

      const success = await audioProcessor.connectTrack(
        mockTrack,
        mockContext,
        mockParticipant
      );

      const duration = performanceMonitor.endTimer("retry-performance");

      expect(success).toBe(true);
      expect(duration).toBeLessThan(TEST_CONFIG.performanceThreshold);

      // Verify retry metrics
      const metrics = audioProcessor.getMetrics();
      expect(metrics.retriesUsed).toBeGreaterThan(0);
    });
  });

  describe("Memory Usage and Leaks", () => {
    it("should maintain stable memory usage during extended operation", async () => {
      const operationCount = 500;
      const memoryReadings = [];

      // Record initial memory
      if (process.memoryUsage) {
        memoryReadings.push(process.memoryUsage().heapUsed);
      }

      for (let i = 0; i < operationCount; i++) {
        // Simulate typical operations
        const participant = testUtils.generateMockParticipant(
          `memory-test-${i}`
        );
        const mockContext = testUtils.generateMockContext();

        await agentManager.handleParticipant(mockContext, participant);

        // Record metrics
        metricsCollector.incrementCounter("agentInitializations");
        metricsCollector.recordTranscription(0.9, true);
        metricsCollector.recordConnection(true, 100);

        // Record memory every 50 operations
        if (i % 50 === 0 && process.memoryUsage) {
          memoryReadings.push(process.memoryUsage().heapUsed);

          // Force garbage collection if available
          if (global.gc) {
            global.gc();
          }
        }
      }

      if (memoryReadings.length > 2) {
        const initialMemory = memoryReadings[0];
        const finalMemory = memoryReadings[memoryReadings.length - 1];
        const memoryGrowth = finalMemory - initialMemory;
        const memoryGrowthMB = memoryGrowth / (1024 * 1024);

        console.log(`Memory Usage:
          Initial: ${(initialMemory / 1024 / 1024).toFixed(2)} MB
          Final: ${(finalMemory / 1024 / 1024).toFixed(2)} MB
          Growth: ${memoryGrowthMB.toFixed(2)} MB
          Operations: ${operationCount}`);

        // Memory growth should be reasonable (less than 100MB for this test)
        expect(memoryGrowthMB).toBeLessThan(100);

        // Check for memory leaks (growth should be sub-linear)
        const growthRate = memoryGrowthMB / operationCount;
        expect(growthRate).toBeLessThan(0.2); // Less than 0.2MB per operation
      }
    });

    it("should clean up resources properly", async () => {
      const resourceCount = 100;
      const resources = [];

      // Create multiple resources
      for (let i = 0; i < resourceCount; i++) {
        const manager = new EnhancedAgentManager();
        vi.stubGlobal("process", { env: testUtils.createMockEnvironment() });
        await manager.initialize();
        resources.push(manager);
      }

      // Record memory after resource creation
      const memoryAfterCreation = process.memoryUsage
        ? process.memoryUsage().heapUsed
        : 0;

      // Clear references to resources
      resources.length = 0;

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
        await testUtils.delay(100); // Allow GC to complete
      }

      const memoryAfterCleanup = process.memoryUsage
        ? process.memoryUsage().heapUsed
        : 0;
      const memoryFreed = memoryAfterCreation - memoryAfterCleanup;

      console.log(`Resource Cleanup:
        Memory after creation: ${(memoryAfterCreation / 1024 / 1024).toFixed(
          2
        )} MB
        Memory after cleanup: ${(memoryAfterCleanup / 1024 / 1024).toFixed(
          2
        )} MB
        Memory freed: ${(memoryFreed / 1024 / 1024).toFixed(2)} MB`);

      // Should free at least some memory (or at least not grow significantly)
      expect(memoryAfterCleanup).toBeLessThanOrEqual(memoryAfterCreation * 1.1);
    });
  });

  describe("Scalability Testing", () => {
    it("should scale linearly with participant count", async () => {
      const participantCounts = [1, 5, 10, 20];
      const scalabilityResults = [];

      for (const count of participantCounts) {
        performanceMonitor.startTimer(`scale-${count}`);

        const participants = Array.from({ length: count }, (_, i) =>
          testUtils.generateMockParticipant(`scale-participant-${i}`)
        );

        const mockContext = testUtils.generateMockContext();

        const sessionPromises = participants.map((participant) =>
          agentManager.handleParticipant(mockContext, participant)
        );

        const sessions = await Promise.all(sessionPromises);
        const duration = performanceMonitor.endTimer(`scale-${count}`);

        scalabilityResults.push({
          participantCount: count,
          duration,
          sessionsCreated: sessions.length,
          averageTimePerParticipant: duration / count,
        });

        expect(sessions.length).toBe(count);
      }

      // Analyze scalability
      console.log("Scalability Results:");
      scalabilityResults.forEach((result) => {
        console.log(
          `  ${result.participantCount} participants: ${result.duration.toFixed(
            2
          )}ms (${result.averageTimePerParticipant.toFixed(2)}ms/participant)`
        );
      });

      // Check that average time per participant doesn't grow exponentially
      const firstResult = scalabilityResults[0];
      const lastResult = scalabilityResults[scalabilityResults.length - 1];

      const scalabilityRatio =
        lastResult.averageTimePerParticipant /
        firstResult.averageTimePerParticipant;
      expect(scalabilityRatio).toBeLessThan(3); // Should not be more than 3x slower per participant
    });

    it("should handle burst traffic efficiently", async () => {
      const burstSize = 50;
      const burstInterval = 100; // ms

      performanceMonitor.startTimer("burst-test");

      // Create burst of participants
      const burstPromises = Array.from({ length: burstSize }, async (_, i) => {
        // Stagger the requests slightly to simulate real burst
        await testUtils.delay(Math.random() * burstInterval);

        const participant = testUtils.generateMockParticipant(`burst-${i}`);
        const mockContext = testUtils.generateMockContext();

        const start = performance.now();
        const session = await agentManager.handleParticipant(
          mockContext,
          participant
        );
        const duration = performance.now() - start;

        return { session, duration, index: i };
      });

      const results = await Promise.all(burstPromises);
      const totalDuration = performanceMonitor.endTimer("burst-test");

      const successfulSessions = results.filter((r) => r.session);
      const averageResponseTime =
        results.reduce((sum, r) => sum + r.duration, 0) / results.length;

      expect(successfulSessions.length).toBeGreaterThan(burstSize * 0.9); // 90% success rate
      expect(averageResponseTime).toBeLessThan(
        TEST_CONFIG.performanceThreshold
      );
      expect(totalDuration).toBeLessThan(TEST_CONFIG.performanceThreshold * 2);

      console.log(`Burst Test Results:
        Burst size: ${burstSize}
        Successful sessions: ${successfulSessions.length}
        Success rate: ${((successfulSessions.length / burstSize) * 100).toFixed(
          1
        )}%
        Average response time: ${averageResponseTime.toFixed(2)}ms
        Total duration: ${totalDuration.toFixed(2)}ms`);
    });
  });

  describe("Stress Testing", () => {
    it("should maintain functionality under extreme load", async () => {
      const extremeLoadDuration = 2000; // 2 seconds
      const operationsPerSecond = 100;
      const totalOperations =
        (extremeLoadDuration / 1000) * operationsPerSecond;

      let operationsCompleted = 0;
      let operationsFailed = 0;

      performanceMonitor.startTimer("stress-test");

      const stressTestPromise = new Promise((resolve) => {
        const interval = setInterval(async () => {
          try {
            // Perform various operations rapidly
            const operations = [
              () => metricsCollector.incrementCounter("agentInitializations"),
              () =>
                metricsCollector.recordTranscription(
                  Math.random(),
                  Math.random() > 0.5
                ),
              () =>
                metricsCollector.recordConnection(
                  Math.random() > 0.1,
                  Math.random() * 1000
                ),
              () => metricsCollector.recordError("STRESS_ERROR", "medium"),
              () => metricsCollector.collectSystemMetrics(),
            ];

            const operation =
              operations[Math.floor(Math.random() * operations.length)];
            await operation();
            operationsCompleted++;
          } catch (error) {
            operationsFailed++;
          }

          if (operationsCompleted + operationsFailed >= totalOperations) {
            clearInterval(interval);
            resolve();
          }
        }, 1000 / operationsPerSecond);

        // Safety timeout
        setTimeout(() => {
          clearInterval(interval);
          resolve();
        }, extremeLoadDuration + 1000);
      });

      await stressTestPromise;
      const stressDuration = performanceMonitor.endTimer("stress-test");

      const successRate =
        operationsCompleted / (operationsCompleted + operationsFailed);
      const actualOpsPerSecond = operationsCompleted / (stressDuration / 1000);

      console.log(`Stress Test Results:
        Duration: ${stressDuration.toFixed(2)}ms
        Operations completed: ${operationsCompleted}
        Operations failed: ${operationsFailed}
        Success rate: ${(successRate * 100).toFixed(1)}%
        Actual ops/sec: ${actualOpsPerSecond.toFixed(0)}`);

      expect(successRate).toBeGreaterThan(0.95); // 95% success rate under stress
      expect(operationsCompleted).toBeGreaterThan(totalOperations * 0.8); // At least 80% of target operations
    });
  });
});
