/**
 * Test Setup and Configuration
 * Global test utilities and mocks for the testing suite
 */

import { vi } from "vitest";
import { config } from "dotenv";

// Load test environment variables
config({ path: ".env.test" });

// Global test configuration
export const TEST_CONFIG = {
  timeout: 10000,
  retries: 2,
  mockDelay: 100,
  performanceThreshold: 1000,
  loadTestDuration: 5000,
  concurrentConnections: 10,
};

// Mock LiveKit Agents modules globally
vi.mock("@livekit/agents", () => ({
  WorkerOptions: vi.fn(),
  cli: {
    runApp: vi.fn(),
  },
  defineAgent: vi.fn((config) => config),
  multimodal: {
    MultimodalAgent: vi.fn().mockImplementation(() => ({
      start: vi.fn().mockResolvedValue({
        on: vi.fn(),
      }),
    })),
  },
}));

vi.mock("@livekit/agents-plugin-deepgram", () => ({
  STT: vi.fn().mockImplementation((config) => ({
    config,
    stream: vi.fn().mockReturnValue({
      input: {
        pushTrack: vi.fn(),
        push: vi.fn(),
        write: vi.fn(),
        put: vi.fn(),
      },
      [Symbol.asyncIterator]: async function* () {
        yield { text: "test transcription", confidence: 0.9, isFinal: false };
        yield {
          text: "test transcription complete",
          confidence: 0.95,
          isFinal: true,
        };
      },
    }),
  })),
}));

vi.mock("@livekit/agents-plugin-openai", () => ({
  LLM: vi.fn().mockImplementation(() => ({
    model: "gpt-4o-mini",
    generate: vi.fn().mockResolvedValue({ text: "response" }),
  })),
  TTS: vi.fn().mockImplementation(() => ({
    model: "tts-1",
    synthesize: vi.fn().mockResolvedValue(null),
  })),
}));

// Mock fetch for API calls
global.fetch = vi.fn();

// Mock WebSocket for LiveKit connections
global.WebSocket = vi.fn().mockImplementation(() => ({
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  send: vi.fn(),
  close: vi.fn(),
  readyState: 1, // OPEN
}));

// Mock MediaDevices for audio testing
Object.defineProperty(global, "navigator", {
  value: {
    mediaDevices: {
      getUserMedia: vi.fn().mockResolvedValue({
        getAudioTracks: vi.fn().mockReturnValue([
          {
            kind: "audio",
            stop: vi.fn(),
          },
        ]),
      }),
    },
  },
  writable: true,
});

// Mock console methods for cleaner test output
const originalConsole = { ...console };
export const mockConsole = {
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
};

export function enableConsoleMocks() {
  Object.assign(console, mockConsole);
}

export function restoreConsole() {
  Object.assign(console, originalConsole);
}

// Test utilities
export class TestUtils {
  static async delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  static generateMockTrack(id = "test-track") {
    return {
      kind: "audio",
      info: { sid: id },
      ffi_handle: { mockData: "audio-data" },
    };
  }

  static generateMockParticipant(identity = "test-participant") {
    return {
      identity,
      publishData: vi.fn().mockResolvedValue(true),
    };
  }

  static generateMockRoom(name = "test-room") {
    return {
      name,
      on: vi.fn(),
      localParticipant: {
        publishData: vi.fn().mockResolvedValue(true),
        publishTrack: vi.fn().mockResolvedValue(true),
      },
    };
  }

  static generateMockContext() {
    return {
      connect: vi.fn().mockResolvedValue(true),
      room: this.generateMockRoom(),
      waitForParticipant: vi
        .fn()
        .mockResolvedValue(this.generateMockParticipant()),
    };
  }

  static createMockEnvironment(vars = {}) {
    const defaultVars = {
      LIVEKIT_URL: "wss://test.livekit.cloud",
      LIVEKIT_API_KEY: "test-api-key",
      LIVEKIT_API_SECRET: "test-api-secret",
      DEEPGRAM_API_KEY: "test-deepgram-key",
      OPENAI_API_KEY: "test-openai-key",
    };

    return { ...defaultVars, ...vars };
  }

  static async measurePerformance(fn, label = "operation") {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;

    return {
      result,
      duration,
      label,
      withinThreshold: duration < TEST_CONFIG.performanceThreshold,
    };
  }

  static async runConcurrentTests(testFn, count = 5) {
    const promises = Array.from({ length: count }, (_, i) =>
      testFn(i).catch((error) => ({ error, index: i }))
    );

    const results = await Promise.all(promises);
    const successes = results.filter((r) => !r.error);
    const failures = results.filter((r) => r.error);

    return {
      total: count,
      successes: successes.length,
      failures: failures.length,
      successRate: successes.length / count,
      results,
    };
  }

  static generateTranscriptionData(overrides = {}) {
    return {
      type: "transcription",
      text: "Hello world",
      confidence: 0.9,
      isFinal: false,
      timestamp: Date.now(),
      participant: "test-participant",
      metadata: {
        agentType: "test",
        processingTime: Date.now(),
      },
      ...overrides,
    };
  }

  static validateTranscriptionData(data) {
    const required = [
      "type",
      "text",
      "confidence",
      "isFinal",
      "timestamp",
      "participant",
    ];
    const missing = required.filter((field) => !(field in data));

    return {
      valid: missing.length === 0,
      missing,
      data,
    };
  }
}

// Performance monitoring utilities
export class PerformanceMonitor {
  constructor() {
    this.metrics = {
      operations: [],
      memory: [],
      timing: new Map(),
    };
  }

  startTimer(label) {
    this.metrics.timing.set(label, performance.now());
  }

  endTimer(label) {
    const start = this.metrics.timing.get(label);
    if (start) {
      const duration = performance.now() - start;
      this.metrics.operations.push({ label, duration, timestamp: Date.now() });
      this.metrics.timing.delete(label);
      return duration;
    }
    return null;
  }

  recordMemory() {
    if (process.memoryUsage) {
      const memory = process.memoryUsage();
      this.metrics.memory.push({
        ...memory,
        timestamp: Date.now(),
      });
    }
  }

  getStats() {
    const operations = this.metrics.operations;
    if (operations.length === 0) return null;

    const durations = operations.map((op) => op.duration);
    const avg = durations.reduce((a, b) => a + b, 0) / durations.length;
    const min = Math.min(...durations);
    const max = Math.max(...durations);

    return {
      count: operations.length,
      average: avg,
      min,
      max,
      operations,
      memory: this.metrics.memory,
    };
  }

  reset() {
    this.metrics = {
      operations: [],
      memory: [],
      timing: new Map(),
    };
  }
}

// Export global instances
export const performanceMonitor = new PerformanceMonitor();
export const testUtils = TestUtils;
