#!/usr/bin/env node

/**
 * Test script for TranscriptionDataManager and TranscriptionUIManager
 * Tests enhanced transcription data handling and UI management
 */

// Mock DOM environment for testing
global.document = {
  createElement: (tag) => ({
    className: "",
    innerHTML: "",
    style: {},
    dataset: {},
    appendChild: () => {},
    remove: () => {},
    querySelector: () => null,
    querySelectorAll: () => [],
    addEventListener: () => {},
  }),
  head: {
    insertAdjacentHTML: () => {},
  },
  getElementById: () => null,
};

global.window = {
  getComputedStyle: () => ({ display: "block" }),
};

// Import the managers
import { TranscriptionDataManager } from "../public/transcription-data-manager.js";

async function runTests() {
  console.log("🧪 Starting TranscriptionDataManager tests...\n");

  // Test 1: Basic initialization
  console.log("Test 1: Basic initialization");
  const dataManager = new TranscriptionDataManager({
    maxInterimAge: 3000,
    maxHistorySize: 100,
    enableSpeakerDiarization: true,
    enableMultiParticipant: true,
  });

  console.log(`✅ DataManager initialized with options`);
  console.log(`Stats:`, dataManager.getStatistics());

  // Test 2: Process interim transcription
  console.log("\nTest 2: Process interim transcription");
  let interimEventFired = false;

  dataManager.on("interimResult", (data) => {
    interimEventFired = true;
    console.log(`📝 Interim event fired:`, data.data.text);
  });

  const interimData = {
    type: "interim",
    text: "Hello world",
    confidence: 0.8,
    timestamp: Date.now(),
    participant: "user-123",
    speaker: { id: 0, confidence: 0.9 },
    isFinal: false,
  };

  const processed1 = dataManager.processTranscriptionData(interimData);
  console.log(`Result: ${processed1 ? "SUCCESS" : "FAILED"}`);
  console.log(`Interim event fired: ${interimEventFired ? "YES" : "NO"}`);
  console.log(
    `Interim results count: ${dataManager.getInterimResults().length}`
  );

  // Test 3: Process final transcription
  console.log("\nTest 3: Process final transcription");
  let finalEventFired = false;

  dataManager.on("finalResult", (data) => {
    finalEventFired = true;
    console.log(`✅ Final event fired:`, data.data.text);
  });

  const finalData = {
    type: "final",
    text: "Hello world!",
    confidence: 0.95,
    timestamp: Date.now(),
    participant: "user-123",
    speaker: { id: 0, confidence: 0.9 },
    isFinal: true,
  };

  const processed2 = dataManager.processTranscriptionData(finalData);
  console.log(`Result: ${processed2 ? "SUCCESS" : "FAILED"}`);
  console.log(`Final event fired: ${finalEventFired ? "YES" : "NO"}`);
  console.log(`Final results count: ${dataManager.getFinalResults().length}`);

  // Test 4: Speaker diarization
  console.log("\nTest 4: Speaker diarization");
  const speakerData = {
    type: "final",
    text: "This is speaker B",
    confidence: 0.85,
    timestamp: Date.now(),
    participant: "user-456",
    speaker: { id: 1, confidence: 0.8 },
    isFinal: true,
  };

  dataManager.processTranscriptionData(speakerData);
  const stats = dataManager.getStatistics();
  console.log(`Total speakers: ${stats.totalSpeakers}`);
  console.log(`Speaker distribution:`, stats.speakerDistribution);

  // Test 5: Multi-participant handling
  console.log("\nTest 5: Multi-participant handling");

  // Session start for new participant
  dataManager.processTranscriptionData({
    type: "session_start",
    participant: "user-789",
    timestamp: Date.now(),
  });

  // Transcription from new participant
  dataManager.processTranscriptionData({
    type: "final",
    text: "Hello from participant 2",
    confidence: 0.9,
    timestamp: Date.now(),
    participant: "user-789",
    speaker: { id: 2, confidence: 0.85 },
    isFinal: true,
  });

  const updatedStats = dataManager.getStatistics();
  console.log(`Active participants: ${updatedStats.activeParticipants}`);
  console.log(`Total transcriptions: ${updatedStats.totalTranscriptions}`);

  // Test 6: Data validation
  console.log("\nTest 6: Data validation");

  const invalidData = [
    { type: "invalid" }, // Missing text
    { text: "test" }, // Missing type
    { type: "final", text: "" }, // Empty text
    null, // Null data
    "invalid", // String instead of object
  ];

  let validationResults = [];
  invalidData.forEach((data, index) => {
    const result = dataManager.processTranscriptionData(data);
    validationResults.push(result);
    console.log(
      `Invalid data ${index + 1}: ${
        result ? "ACCEPTED" : "REJECTED"
      } (expected: REJECTED)`
    );
  });

  const allRejected = validationResults.every((result) => !result);
  console.log(`All invalid data rejected: ${allRejected ? "YES" : "NO"}`);

  // Test 7: Statistics and metrics
  console.log("\nTest 7: Statistics and metrics");
  const finalStats = dataManager.getStatistics();
  console.log("Final statistics:", {
    totalTranscriptions: finalStats.totalTranscriptions,
    interimCount: finalStats.interimCount,
    finalCount: finalStats.finalCount,
    activeParticipants: finalStats.activeParticipants,
    totalSpeakers: finalStats.totalSpeakers,
    averageConfidence: finalStats.averageConfidence.toFixed(2),
  });

  // Test 8: Data export/import
  console.log("\nTest 8: Data export/import");
  const exportedData = dataManager.exportData();
  console.log(
    `Exported data contains ${exportedData.finalResults.length} final results`
  );

  const newDataManager = new TranscriptionDataManager();
  const importSuccess = newDataManager.importData(exportedData);
  console.log(`Import success: ${importSuccess ? "YES" : "NO"}`);
  console.log(
    `Imported final results: ${newDataManager.getFinalResults().length}`
  );

  // Test 9: Cleanup and resource management
  console.log("\nTest 9: Cleanup and resource management");

  // Add some interim results that should expire
  dataManager.processTranscriptionData({
    type: "interim",
    text: "This should expire",
    timestamp: Date.now() - 10000, // 10 seconds ago
    participant: "user-test",
  });

  console.log(
    `Interim results before cleanup: ${dataManager.getInterimResults().length}`
  );

  // Wait for cleanup (simulate)
  setTimeout(() => {
    console.log(
      `Interim results after cleanup: ${dataManager.getInterimResults().length}`
    );
  }, 100);

  // Test 10: Memory management
  console.log("\nTest 10: Memory management");

  // Add many results to test history limit
  for (let i = 0; i < 150; i++) {
    dataManager.processTranscriptionData({
      type: "final",
      text: `Test message ${i}`,
      confidence: 0.8,
      timestamp: Date.now() + i,
      participant: "stress-test",
      isFinal: true,
    });
  }

  const finalResultsCount = dataManager.getFinalResults().length;
  console.log(`Final results count after adding 150: ${finalResultsCount}`);
  console.log(
    `History limit respected: ${finalResultsCount <= 100 ? "YES" : "NO"}`
  );

  // Cleanup
  dataManager.destroy();
  newDataManager.destroy();

  console.log("\n🎉 All TranscriptionDataManager tests completed!");
}

// Run tests
runTests().catch(console.error);
