#!/usr/bin/env node

/**
 * Comprehensive Test Runner
 * Orchestrates all test suites and generates detailed reports
 */

import { spawn } from "child_process";
import { writeFileSync, mkdirSync, existsSync } from "fs";
import { join } from "path";

class TestRunner {
  constructor() {
    this.results = {
      unit: null,
      integration: null,
      performance: null,
      overall: null,
    };

    this.config = {
      timeout: 30000,
      retries: 2,
      coverage: true,
      parallel: true,
      verbose: true,
    };

    this.reportDir = "test-reports";
    this.ensureReportDirectory();
  }

  ensureReportDirectory() {
    if (!existsSync(this.reportDir)) {
      mkdirSync(this.reportDir, { recursive: true });
    }
  }

  async runTestSuite(suiteName, pattern, options = {}) {
    console.log(`\n🧪 Running ${suiteName} tests...`);
    console.log("=".repeat(50));

    const startTime = Date.now();

    const vitestArgs = [
      "run",
      pattern,
      "--reporter=verbose",
      "--reporter=json",
      `--outputFile=${this.reportDir}/${suiteName}-results.json`,
      options.timeout
        ? `--testTimeout=${options.timeout}`
        : `--testTimeout=${this.config.timeout}`,
    ];

    if (this.config.coverage && suiteName !== "performance") {
      vitestArgs.push("--coverage");
    }

    if (options.parallel !== false && this.config.parallel) {
      vitestArgs.push("--run");
    }

    try {
      const result = await this.executeVitest(vitestArgs);
      const duration = Date.now() - startTime;

      const testResult = {
        suite: suiteName,
        success: result.exitCode === 0,
        duration,
        exitCode: result.exitCode,
        stdout: result.stdout,
        stderr: result.stderr,
      };

      this.results[suiteName] = testResult;

      if (testResult.success) {
        console.log(
          `✅ ${suiteName} tests completed successfully in ${duration}ms`
        );
      } else {
        console.log(
          `❌ ${suiteName} tests failed (exit code: ${result.exitCode})`
        );
        if (result.stderr) {
          console.log("Error output:", result.stderr);
        }
      }

      return testResult;
    } catch (error) {
      console.error(`❌ Failed to run ${suiteName} tests:`, error.message);

      const testResult = {
        suite: suiteName,
        success: false,
        duration: Date.now() - startTime,
        error: error.message,
      };

      this.results[suiteName] = testResult;
      return testResult;
    }
  }

  executeVitest(args) {
    return new Promise((resolve, reject) => {
      const child = spawn("npx", ["vitest", ...args], {
        stdio: ["pipe", "pipe", "pipe"],
        env: { ...process.env, NODE_ENV: "test" },
      });

      let stdout = "";
      let stderr = "";

      child.stdout.on("data", (data) => {
        const output = data.toString();
        stdout += output;
        if (this.config.verbose) {
          process.stdout.write(output);
        }
      });

      child.stderr.on("data", (data) => {
        const output = data.toString();
        stderr += output;
        if (this.config.verbose) {
          process.stderr.write(output);
        }
      });

      child.on("close", (exitCode) => {
        resolve({ exitCode, stdout, stderr });
      });

      child.on("error", (error) => {
        reject(error);
      });
    });
  }

  async runAllTests() {
    console.log("🚀 Starting Comprehensive Test Suite");
    console.log("=".repeat(60));

    const overallStartTime = Date.now();

    // Run test suites in sequence to avoid resource conflicts
    const suites = [
      {
        name: "unit",
        pattern: "test/unit/**/*.test.js",
        options: { timeout: 10000 },
      },
      {
        name: "integration",
        pattern: "test/integration/**/*.test.js",
        options: { timeout: 20000 },
      },
      {
        name: "performance",
        pattern: "test/performance/**/*.test.js",
        options: { timeout: 60000, parallel: false },
      },
    ];

    const results = [];

    for (const suite of suites) {
      const result = await this.runTestSuite(
        suite.name,
        suite.pattern,
        suite.options
      );
      results.push(result);

      // Add delay between suites to allow cleanup
      await this.delay(1000);
    }

    const overallDuration = Date.now() - overallStartTime;

    // Calculate overall results
    const successfulSuites = results.filter((r) => r.success).length;
    const totalSuites = results.length;
    const overallSuccess = successfulSuites === totalSuites;

    this.results.overall = {
      success: overallSuccess,
      duration: overallDuration,
      suites: {
        total: totalSuites,
        successful: successfulSuites,
        failed: totalSuites - successfulSuites,
      },
      results,
    };

    // Generate reports
    await this.generateReports();

    // Print summary
    this.printSummary();

    return this.results.overall;
  }

  async generateReports() {
    console.log("\n📊 Generating test reports...");

    // Generate HTML report
    await this.generateHTMLReport();

    // Generate JSON report
    await this.generateJSONReport();

    // Generate markdown report
    await this.generateMarkdownReport();

    console.log(`📁 Reports generated in ${this.reportDir}/`);
  }

  async generateHTMLReport() {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Results - LiveKit Agents Optimization</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .metric.success { border-left: 4px solid #28a745; }
        .metric.failure { border-left: 4px solid #dc3545; }
        .metric.info { border-left: 4px solid #17a2b8; }
        .metric-value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .metric-label { color: #666; font-size: 0.9em; }
        .suite-results { margin-bottom: 30px; }
        .suite { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 15px; }
        .suite.success { border-left: 4px solid #28a745; }
        .suite.failure { border-left: 4px solid #dc3545; }
        .suite-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .suite-name { font-size: 1.2em; font-weight: bold; }
        .suite-duration { color: #666; }
        .suite-status { padding: 4px 12px; border-radius: 4px; color: white; font-size: 0.9em; }
        .suite-status.success { background: #28a745; }
        .suite-status.failure { background: #dc3545; }
        pre { background: #f1f1f1; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Results</h1>
            <h2>LiveKit Agents Optimization</h2>
            <p>Generated on ${new Date().toLocaleString()}</p>
        </div>
        
        <div class="summary">
            <div class="metric ${
              this.results.overall.success ? "success" : "failure"
            }">
                <div class="metric-value">${
                  this.results.overall.success ? "✅" : "❌"
                }</div>
                <div class="metric-label">Overall Status</div>
            </div>
            <div class="metric info">
                <div class="metric-value">${
                  this.results.overall.suites.successful
                }/${this.results.overall.suites.total}</div>
                <div class="metric-label">Suites Passed</div>
            </div>
            <div class="metric info">
                <div class="metric-value">${(
                  this.results.overall.duration / 1000
                ).toFixed(1)}s</div>
                <div class="metric-label">Total Duration</div>
            </div>
        </div>
        
        <div class="suite-results">
            <h3>Test Suite Results</h3>
            ${this.results.overall.results
              .map(
                (result) => `
                <div class="suite ${result.success ? "success" : "failure"}">
                    <div class="suite-header">
                        <div class="suite-name">${result.suite.toUpperCase()} Tests</div>
                        <div class="suite-duration">${result.duration}ms</div>
                        <div class="suite-status ${
                          result.success ? "success" : "failure"
                        }">
                            ${result.success ? "PASSED" : "FAILED"}
                        </div>
                    </div>
                    ${result.error ? `<pre>Error: ${result.error}</pre>` : ""}
                    ${
                      result.stderr && !result.success
                        ? `<pre>Error Output:\n${result.stderr}</pre>`
                        : ""
                    }
                </div>
            `
              )
              .join("")}
        </div>
        
        <div class="footer">
            <p><small>Generated by LiveKit Agents Test Runner</small></p>
        </div>
    </div>
</body>
</html>`;

    writeFileSync(join(this.reportDir, "test-report.html"), html);
  }

  async generateJSONReport() {
    const jsonReport = {
      timestamp: new Date().toISOString(),
      overall: this.results.overall,
      suites: this.results,
      environment: {
        node: process.version,
        platform: process.platform,
        arch: process.arch,
      },
    };

    writeFileSync(
      join(this.reportDir, "test-results.json"),
      JSON.stringify(jsonReport, null, 2)
    );
  }

  async generateMarkdownReport() {
    const markdown = `# Test Results - LiveKit Agents Optimization

Generated on: ${new Date().toLocaleString()}

## Summary

- **Overall Status**: ${
      this.results.overall.success ? "✅ PASSED" : "❌ FAILED"
    }
- **Suites Passed**: ${this.results.overall.suites.successful}/${
      this.results.overall.suites.total
    }
- **Total Duration**: ${(this.results.overall.duration / 1000).toFixed(1)}s

## Test Suite Results

${this.results.overall.results
  .map(
    (result) => `
### ${result.suite.toUpperCase()} Tests

- **Status**: ${result.success ? "✅ PASSED" : "❌ FAILED"}
- **Duration**: ${result.duration}ms
- **Exit Code**: ${result.exitCode || "N/A"}

${result.error ? `**Error**: ${result.error}` : ""}
${
  result.stderr && !result.success
    ? `\n**Error Output**:\n\`\`\`\n${result.stderr}\n\`\`\``
    : ""
}
`
  )
  .join("")}

## Environment

- **Node.js**: ${process.version}
- **Platform**: ${process.platform}
- **Architecture**: ${process.arch}

---
*Generated by LiveKit Agents Test Runner*
`;

    writeFileSync(join(this.reportDir, "test-report.md"), markdown);
  }

  printSummary() {
    console.log("\n" + "=".repeat(60));
    console.log("📊 TEST SUMMARY");
    console.log("=".repeat(60));

    console.log(
      `Overall Status: ${
        this.results.overall.success ? "✅ PASSED" : "❌ FAILED"
      }`
    );
    console.log(
      `Total Duration: ${(this.results.overall.duration / 1000).toFixed(1)}s`
    );
    console.log(
      `Suites: ${this.results.overall.suites.successful}/${this.results.overall.suites.total} passed`
    );

    console.log("\nSuite Details:");
    this.results.overall.results.forEach((result) => {
      const status = result.success ? "✅" : "❌";
      const duration = `${result.duration}ms`;
      console.log(
        `  ${status} ${result.suite.padEnd(12)} ${duration.padStart(8)}`
      );
    });

    console.log("\nReports generated:");
    console.log(`  📄 HTML: ${this.reportDir}/test-report.html`);
    console.log(`  📄 JSON: ${this.reportDir}/test-results.json`);
    console.log(`  📄 Markdown: ${this.reportDir}/test-report.md`);

    console.log("\n" + "=".repeat(60));
  }

  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const runner = new TestRunner();

  // Parse command line arguments
  if (args.includes("--help") || args.includes("-h")) {
    console.log(`
LiveKit Agents Test Runner

Usage: node test-runner.js [options]

Options:
  --unit          Run only unit tests
  --integration   Run only integration tests  
  --performance   Run only performance tests
  --no-coverage   Disable coverage reporting
  --no-parallel   Disable parallel execution
  --quiet         Reduce output verbosity
  --help, -h      Show this help message

Examples:
  node test-runner.js                    # Run all tests
  node test-runner.js --unit             # Run only unit tests
  node test-runner.js --no-coverage      # Run without coverage
`);
    process.exit(0);
  }

  // Configure runner based on arguments
  if (args.includes("--no-coverage")) {
    runner.config.coverage = false;
  }

  if (args.includes("--no-parallel")) {
    runner.config.parallel = false;
  }

  if (args.includes("--quiet")) {
    runner.config.verbose = false;
  }

  try {
    let results;

    if (args.includes("--unit")) {
      results = await runner.runTestSuite("unit", "test/unit/**/*.test.js");
    } else if (args.includes("--integration")) {
      results = await runner.runTestSuite(
        "integration",
        "test/integration/**/*.test.js",
        { timeout: 20000 }
      );
    } else if (args.includes("--performance")) {
      results = await runner.runTestSuite(
        "performance",
        "test/performance/**/*.test.js",
        { timeout: 60000 }
      );
    } else {
      results = await runner.runAllTests();
    }

    process.exit(results.success ? 0 : 1);
  } catch (error) {
    console.error("❌ Test runner failed:", error.message);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default TestRunner;
