# LiveKit Agents + Deepgram Transcription Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# LiveKit Configuration
# Get these from your LiveKit Cloud dashboard: https://cloud.livekit.io/
# Or from your self-hosted LiveKit server
LIVEKIT_URL=wss://your-project.livekit.cloud
LIVEKIT_API_KEY=APIxxxxxxxxxxxxxxx
LIVEKIT_API_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Deepgram Configuration
# Get your API key from: https://console.deepgram.com/
# Ensure your account has sufficient credits for transcription
DEEPGRAM_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# OpenAI Configuration (Optional)
# Required for enhanced MultimodalAgent features (LLM + TTS)
# Get your API key from: https://platform.openai.com/
# If not provided, the system will use transcription-only mode
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Application Configuration
# Environment: development, production, or test
NODE_ENV=development

# Server port for the web interface
PORT=3000

# Logging Configuration
# Levels: error, warn, info, debug
LOG_LEVEL=info

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Skip connectivity tests during validation (faster startup)
# Set to 'true' to skip API connectivity tests
SKIP_CONNECTIVITY_TESTS=false

# Agent Configuration
# Maximum retry attempts for audio connection
MAX_AUDIO_RETRIES=3

# Connection timeout in milliseconds
CONNECTION_TIMEOUT=10000

# Enable detailed debug logging for audio processing
DEBUG_AUDIO_PROCESSING=false

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development-specific settings (only used when NODE_ENV=development)

# Enable hot reload for agent development
DEV_HOT_RELOAD=false

# Mock external services for testing
MOCK_SERVICES=false

# Additional debug output
VERBOSE_LOGGING=false

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================

# Production-specific settings (only used when NODE_ENV=production)

# Enable performance monitoring
ENABLE_METRICS=true

# Health check interval in seconds
HEALTH_CHECK_INTERVAL=30

# Maximum concurrent connections
MAX_CONNECTIONS=100

# Enable request rate limiting
ENABLE_RATE_LIMITING=true

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Docker and Kubernetes deployment settings

# Container registry for deployment
CONTAINER_REGISTRY=your-registry.com

# Image tag for deployment
IMAGE_TAG=latest

# Kubernetes namespace
K8S_NAMESPACE=livekit-transcription

# Resource limits
MEMORY_LIMIT=512Mi
CPU_LIMIT=500m

# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================

# Prometheus metrics endpoint
METRICS_ENDPOINT=/metrics

# Health check endpoint
HEALTH_ENDPOINT=/health

# Log format: json or text
LOG_FORMAT=json

# Enable distributed tracing
ENABLE_TRACING=false

# Tracing service endpoint
TRACING_ENDPOINT=http://jaeger:14268/api/traces

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# CORS settings for web interface
CORS_ORIGIN=*

# Enable HTTPS in production
FORCE_HTTPS=false

# Session secret for web interface
SESSION_SECRET=your-random-session-secret-here

# API rate limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env:
#    cp .env.example .env

# 2. Get your LiveKit credentials:
#    - Go to https://cloud.livekit.io/
#    - Create a new project or select existing
#    - Copy the URL, API Key, and API Secret

# 3. Get your Deepgram API key:
#    - Go to https://console.deepgram.com/
#    - Create an account or log in
#    - Generate a new API key
#    - Ensure your account has sufficient credits

# 4. (Optional) Get OpenAI API key:
#    - Go to https://platform.openai.com/
#    - Create an account or log in
#    - Generate a new API key
#    - Ensure your account has sufficient credits

# 5. Validate your configuration:
#    npm run validate

# 6. Start the application:
#    npm run agent

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# Common Issues:

# 1. "Connection failed" errors:
#    - Check your internet connection
#    - Verify API keys are correct and active
#    - Ensure services are not blocked by firewall

# 2. "Invalid API key" errors:
#    - Double-check API key format
#    - Ensure API key has proper permissions
#    - Check account billing status

# 3. "Audio connection failed" errors:
#    - Verify LiveKit URL is correct
#    - Check LiveKit server status
#    - Ensure WebSocket connections are allowed

# 4. Performance issues:
#    - Increase memory limits in production
#    - Check network latency to services
#    - Monitor CPU and memory usage

# For more help:
# - Check the README.md file
# - Review the troubleshooting documentation
# - Run: npm run validate -- --help