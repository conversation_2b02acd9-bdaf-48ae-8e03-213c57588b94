/**
 * Comprehensive Configuration Validator
 * Validates environment variables, tests API connectivity, and provides setup guidance
 */

import https from "https";
import { URL } from "url";

export class ConfigValidator {
  constructor(options = {}) {
    this.options = {
      timeout: 10000, // 10 seconds
      retries: 2,
      skipConnectivityTests: false,
      ...options,
    };

    this.validationResults = {
      envVars: {},
      connectivity: {},
      overall: false,
      errors: [],
      warnings: [],
      setupGuide: {},
    };
  }

  /**
   * Main validation method - validates all configuration aspects
   */
  async validate() {
    console.log("🔍 Starting comprehensive configuration validation...");

    try {
      // Step 1: Validate environment variables
      await this.validateEnvironmentVariables();

      // Step 2: Validate configuration formats
      await this.validateConfigurationFormats();

      // Step 3: Test API connectivity (unless skipped)
      if (!this.options.skipConnectivityTests) {
        await this.testConnectivity();
      }

      // Step 4: Generate setup guide for any issues
      this.generateSetupGuide();

      // Step 5: Determine overall validation result
      this.determineOverallResult();

      return this.validationResults;
    } catch (error) {
      this.validationResults.errors.push({
        type: "VALIDATION_ERROR",
        message: `Configuration validation failed: ${error.message}`,
        details: error.stack,
      });
      this.validationResults.overall = false;
      return this.validationResults;
    }
  }

  /**
   * Validate all required and optional environment variables
   */
  async validateEnvironmentVariables() {
    console.log("📋 Validating environment variables...");

    const requiredVars = [
      {
        name: "LIVEKIT_URL",
        description: "LiveKit server URL",
        example: "wss://your-project.livekit.cloud",
        validation: (value) => this.validateUrl(value, ["ws:", "wss:"]),
      },
      {
        name: "LIVEKIT_API_KEY",
        description: "LiveKit API key",
        example: "APIxxxxxxxxxxxxxxx",
        validation: (value) => this.validateApiKey(value, "API"),
      },
      {
        name: "LIVEKIT_API_SECRET",
        description: "LiveKit API secret",
        example: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
        validation: (value) => this.validateApiSecret(value),
      },
      {
        name: "DEEPGRAM_API_KEY",
        description: "Deepgram API key",
        example: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
        validation: (value) => this.validateApiKey(value, "deepgram"),
      },
    ];

    const optionalVars = [
      {
        name: "OPENAI_API_KEY",
        description: "OpenAI API key (for enhanced features)",
        example: "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
        validation: (value) => this.validateApiKey(value, "sk-"),
        impact: "MultimodalAgent features will be limited",
      },
      {
        name: "NODE_ENV",
        description: "Node.js environment",
        example: "production",
        validation: (value) =>
          ["development", "production", "test"].includes(value),
        default: "development",
      },
      {
        name: "PORT",
        description: "Server port",
        example: "3000",
        validation: (value) => this.validatePort(value),
        default: "3000",
      },
      {
        name: "LOG_LEVEL",
        description: "Logging level",
        example: "info",
        validation: (value) =>
          ["error", "warn", "info", "debug"].includes(value),
        default: "info",
      },
    ];

    // Validate required variables
    for (const varConfig of requiredVars) {
      const value = process.env[varConfig.name];
      const result = this.validateEnvironmentVariable(varConfig, value, true);
      this.validationResults.envVars[varConfig.name] = result;

      if (!result.valid) {
        this.validationResults.errors.push({
          type: "MISSING_REQUIRED_ENV_VAR",
          variable: varConfig.name,
          message: result.message,
          setupGuide: result.setupGuide,
        });
      }
    }

    // Validate optional variables
    for (const varConfig of optionalVars) {
      const value = process.env[varConfig.name];
      const result = this.validateEnvironmentVariable(varConfig, value, false);
      this.validationResults.envVars[varConfig.name] = result;

      if (!result.valid && value) {
        this.validationResults.warnings.push({
          type: "INVALID_OPTIONAL_ENV_VAR",
          variable: varConfig.name,
          message: result.message,
          impact: varConfig.impact,
        });
      } else if (!value) {
        this.validationResults.warnings.push({
          type: "MISSING_OPTIONAL_ENV_VAR",
          variable: varConfig.name,
          message: `Optional variable ${varConfig.name} not set`,
          impact: varConfig.impact,
          default: varConfig.default,
        });
      }
    }

    console.log(`✅ Environment variables validation complete`);
  }

  /**
   * Validate individual environment variable
   */
  validateEnvironmentVariable(config, value, required) {
    const result = {
      name: config.name,
      value: value ? "[REDACTED]" : null,
      valid: false,
      required,
      message: "",
      setupGuide: "",
    };

    if (!value) {
      if (required) {
        result.message = `Required environment variable ${config.name} is missing`;
        result.setupGuide = `Set ${config.name}=${config.example} in your .env file. ${config.description}.`;
      } else {
        result.valid = true;
        result.message = `Optional variable ${config.name} not set, using default behavior`;
      }
      return result;
    }

    // Validate format if validation function provided
    if (config.validation) {
      try {
        const isValid = config.validation(value);
        if (isValid) {
          result.valid = true;
          result.message = `${config.name} format is valid`;
        } else {
          result.message = `${config.name} has invalid format`;
          result.setupGuide = `Ensure ${config.name} follows the format: ${config.example}. ${config.description}.`;
        }
      } catch (error) {
        result.message = `${config.name} validation failed: ${error.message}`;
        result.setupGuide = `Check ${config.name} format. Expected: ${config.example}`;
      }
    } else {
      result.valid = true;
      result.message = `${config.name} is present`;
    }

    return result;
  }

  /**
   * Validate configuration formats and relationships
   */
  async validateConfigurationFormats() {
    console.log("🔧 Validating configuration formats...");

    // Validate LiveKit URL and API key relationship
    const livekitUrl = process.env.LIVEKIT_URL;
    const livekitApiKey = process.env.LIVEKIT_API_KEY;

    if (livekitUrl && livekitApiKey) {
      try {
        const url = new URL(livekitUrl);
        const isCloud = url.hostname.includes("livekit.cloud");
        const keyFormat = livekitApiKey.startsWith("API");

        if (isCloud && !keyFormat) {
          this.validationResults.warnings.push({
            type: "CONFIG_MISMATCH",
            message:
              "LiveKit Cloud URL detected but API key format seems incorrect",
            suggestion:
              "Ensure you are using the correct API key from LiveKit Cloud dashboard",
          });
        }
      } catch (error) {
        // URL validation already handled in environment variable validation
      }
    }

    console.log("✅ Configuration formats validation complete");
  }

  /**
   * Test connectivity to all required services
   */
  async testConnectivity() {
    console.log("🌐 Testing API connectivity...");

    const connectivityTests = [
      {
        name: "LiveKit",
        test: () => this.testLiveKitConnectivity(),
      },
      {
        name: "Deepgram",
        test: () => this.testDeepgramConnectivity(),
      },
    ];

    // Add OpenAI test if API key is provided
    if (process.env.OPENAI_API_KEY) {
      connectivityTests.push({
        name: "OpenAI",
        test: () => this.testOpenAIConnectivity(),
      });
    }

    for (const testConfig of connectivityTests) {
      try {
        console.log(`🔗 Testing ${testConfig.name} connectivity...`);
        const result = await testConfig.test();
        this.validationResults.connectivity[testConfig.name] = result;

        if (result.success) {
          console.log(`✅ ${testConfig.name} connectivity test passed`);
        } else {
          console.log(
            `❌ ${testConfig.name} connectivity test failed: ${result.error}`
          );
        }
      } catch (error) {
        this.validationResults.connectivity[testConfig.name] = {
          success: false,
          error: error.message,
          timestamp: Date.now(),
        };
        console.log(
          `❌ ${testConfig.name} connectivity test error: ${error.message}`
        );
      }
    }

    console.log("✅ Connectivity tests complete");
  }

  /**
   * Test LiveKit connectivity
   */
  async testLiveKitConnectivity() {
    const url = process.env.LIVEKIT_URL;
    const apiKey = process.env.LIVEKIT_API_KEY;
    const apiSecret = process.env.LIVEKIT_API_SECRET;

    if (!url || !apiKey || !apiSecret) {
      return {
        success: false,
        error: "Missing LiveKit configuration",
        timestamp: Date.now(),
      };
    }

    try {
      // Parse the WebSocket URL to get HTTP URL for health check
      const wsUrl = new URL(url);
      const httpUrl = `https://${wsUrl.hostname}`;

      const result = await this.makeHttpRequest(httpUrl, {
        timeout: this.options.timeout,
        headers: {
          "User-Agent": "LiveKit-Config-Validator/1.0",
        },
      });

      return {
        success: true,
        responseTime: result.responseTime,
        timestamp: Date.now(),
        details: "LiveKit server is reachable",
      };
    } catch (error) {
      return {
        success: false,
        error: `LiveKit connectivity failed: ${error.message}`,
        timestamp: Date.now(),
        troubleshooting: [
          "Check if LIVEKIT_URL is correct",
          "Verify network connectivity",
          "Ensure LiveKit server is running",
          "Check firewall settings",
        ],
      };
    }
  }

  /**
   * Test Deepgram connectivity
   */
  async testDeepgramConnectivity() {
    const apiKey = process.env.DEEPGRAM_API_KEY;

    if (!apiKey) {
      return {
        success: false,
        error: "Missing Deepgram API key",
        timestamp: Date.now(),
      };
    }

    try {
      const result = await this.makeHttpRequest(
        "https://api.deepgram.com/v1/projects",
        {
          timeout: this.options.timeout,
          headers: {
            Authorization: `Token ${apiKey}`,
            "User-Agent": "LiveKit-Config-Validator/1.0",
          },
        }
      );

      return {
        success: true,
        responseTime: result.responseTime,
        timestamp: Date.now(),
        details: "Deepgram API is accessible",
      };
    } catch (error) {
      return {
        success: false,
        error: `Deepgram connectivity failed: ${error.message}`,
        timestamp: Date.now(),
        troubleshooting: [
          "Verify DEEPGRAM_API_KEY is correct",
          "Check Deepgram account status",
          "Ensure API key has proper permissions",
          "Check network connectivity to api.deepgram.com",
        ],
      };
    }
  }

  /**
   * Test OpenAI connectivity
   */
  async testOpenAIConnectivity() {
    const apiKey = process.env.OPENAI_API_KEY;

    if (!apiKey) {
      return {
        success: false,
        error: "Missing OpenAI API key",
        timestamp: Date.now(),
      };
    }

    try {
      const result = await this.makeHttpRequest(
        "https://api.openai.com/v1/models",
        {
          timeout: this.options.timeout,
          headers: {
            Authorization: `Bearer ${apiKey}`,
            "User-Agent": "LiveKit-Config-Validator/1.0",
          },
        }
      );

      return {
        success: true,
        responseTime: result.responseTime,
        timestamp: Date.now(),
        details: "OpenAI API is accessible",
      };
    } catch (error) {
      return {
        success: false,
        error: `OpenAI connectivity failed: ${error.message}`,
        timestamp: Date.now(),
        troubleshooting: [
          "Verify OPENAI_API_KEY is correct",
          "Check OpenAI account billing status",
          "Ensure API key has proper permissions",
          "Check network connectivity to api.openai.com",
        ],
      };
    }
  }

  /**
   * Generate comprehensive setup guide
   */
  generateSetupGuide() {
    console.log("📖 Generating setup guide...");

    const guide = {
      missingRequired: [],
      invalidConfig: [],
      connectivityIssues: [],
      quickStart: [],
      troubleshooting: [],
    };

    // Collect missing required variables
    for (const [varName, result] of Object.entries(
      this.validationResults.envVars
    )) {
      if (result.required && !result.valid) {
        guide.missingRequired.push({
          variable: varName,
          instruction: result.setupGuide,
          priority: "critical",
        });
      }
    }

    // Collect connectivity issues
    for (const [serviceName, result] of Object.entries(
      this.validationResults.connectivity
    )) {
      if (!result.success) {
        guide.connectivityIssues.push({
          service: serviceName,
          error: result.error,
          troubleshooting: result.troubleshooting || [],
          priority:
            serviceName === "LiveKit" || serviceName === "Deepgram"
              ? "critical"
              : "optional",
        });
      }
    }

    // Generate quick start instructions
    guide.quickStart = [
      "1. Copy .env.example to .env: cp .env.example .env",
      "2. Edit .env file with your API keys and configuration",
      "3. Get LiveKit credentials from: https://cloud.livekit.io/",
      "4. Get Deepgram API key from: https://console.deepgram.com/",
      "5. (Optional) Get OpenAI API key from: https://platform.openai.com/",
      "6. Run validation again: npm run validate",
    ];

    // Generate troubleshooting guide
    guide.troubleshooting = [
      {
        issue: "LiveKit connection fails",
        solutions: [
          "Verify LIVEKIT_URL format (should start with wss://)",
          "Check API key and secret are from the same project",
          "Ensure LiveKit server is accessible from your network",
        ],
      },
      {
        issue: "Deepgram API errors",
        solutions: [
          "Verify API key is active and has credits",
          "Check API key permissions in Deepgram console",
          "Ensure network can reach api.deepgram.com",
        ],
      },
      {
        issue: "Agent initialization fails",
        solutions: [
          "Check all required environment variables are set",
          "Verify API keys are valid and active",
          "Review agent logs for specific error messages",
        ],
      },
    ];

    this.validationResults.setupGuide = guide;
    console.log("✅ Setup guide generated");
  }

  /**
   * Determine overall validation result
   */
  determineOverallResult() {
    const hasRequiredEnvVars = Object.values(this.validationResults.envVars)
      .filter((result) => result.required)
      .every((result) => result.valid);

    const hasCriticalConnectivityIssues = Object.entries(
      this.validationResults.connectivity
    )
      .filter(([name]) => ["LiveKit", "Deepgram"].includes(name))
      .some(([, result]) => !result.success);

    this.validationResults.overall =
      hasRequiredEnvVars && !hasCriticalConnectivityIssues;

    if (this.validationResults.overall) {
      console.log("✅ Configuration validation passed");
    } else {
      console.log("❌ Configuration validation failed");
    }
  }

  /**
   * Validation helper methods
   */
  validateUrl(value, allowedProtocols = ["http:", "https:", "ws:", "wss:"]) {
    try {
      const url = new URL(value);
      return allowedProtocols.includes(url.protocol);
    } catch {
      return false;
    }
  }

  validateApiKey(value, prefix) {
    if (!value || typeof value !== "string") return false;
    // 放宽前缀检查，只检查长度
    if (prefix === "API" && !value.startsWith("API")) {
      // LiveKit API key 可能不以API开头，只检查长度
      return value.length >= 10;
    }
    if (prefix === "sk-" && !value.startsWith("sk-")) return false;
    if (prefix === "deepgram") {
      // Deepgram API key 不需要特定前缀，只检查长度
      return value.length >= 20;
    }
    return value.length >= 10; // 更宽松的最小长度
  }

  validateApiSecret(value) {
    if (!value || typeof value !== "string") return false;
    return value.length >= 32; // Minimum reasonable secret length
  }

  validatePort(value) {
    const port = parseInt(value, 10);
    return !isNaN(port) && port > 0 && port <= 65535;
  }

  /**
   * HTTP request helper with timeout and retry
   */
  makeHttpRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const timeout = options.timeout || 5000;

      const req = https.request(
        url,
        {
          method: "GET",
          headers: options.headers || {},
          timeout: timeout,
        },
        (res) => {
          const responseTime = Date.now() - startTime;

          // Consider 2xx and 3xx as success for connectivity test
          if (res.statusCode >= 200 && res.statusCode < 400) {
            resolve({ responseTime, statusCode: res.statusCode });
          } else {
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        }
      );

      req.on("timeout", () => {
        req.destroy();
        reject(new Error(`Request timeout after ${timeout}ms`));
      });

      req.on("error", (error) => {
        reject(error);
      });

      req.end();
    });
  }

  /**
   * Print validation results in a user-friendly format
   */
  printResults() {
    const results = this.validationResults;

    console.log("\n" + "=".repeat(60));
    console.log("📋 CONFIGURATION VALIDATION RESULTS");
    console.log("=".repeat(60));

    // Overall status
    if (results.overall) {
      console.log("✅ Overall Status: PASSED");
    } else {
      console.log("❌ Overall Status: FAILED");
    }

    // Environment variables
    console.log("\n📝 Environment Variables:");
    for (const [name, result] of Object.entries(results.envVars)) {
      const status = result.valid ? "✅" : "❌";
      const required = result.required ? "[REQUIRED]" : "[OPTIONAL]";
      console.log(`  ${status} ${name} ${required}: ${result.message}`);
    }

    // Connectivity tests
    if (Object.keys(results.connectivity).length > 0) {
      console.log("\n🌐 Connectivity Tests:");
      for (const [name, result] of Object.entries(results.connectivity)) {
        const status = result.success ? "✅" : "❌";
        const time = result.responseTime ? ` (${result.responseTime}ms)` : "";
        console.log(
          `  ${status} ${name}${time}: ${
            result.success ? result.details : result.error
          }`
        );
      }
    }

    // Errors
    if (results.errors.length > 0) {
      console.log("\n❌ Errors:");
      results.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error.message}`);
      });
    }

    // Warnings
    if (results.warnings.length > 0) {
      console.log("\n⚠️  Warnings:");
      results.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${warning.message}`);
        if (warning.impact) {
          console.log(`     Impact: ${warning.impact}`);
        }
      });
    }

    // Setup guide for failures
    if (!results.overall && results.setupGuide) {
      console.log("\n📖 Setup Guide:");

      if (results.setupGuide.missingRequired.length > 0) {
        console.log("\n  🔴 Critical Issues:");
        results.setupGuide.missingRequired.forEach((item, index) => {
          console.log(`    ${index + 1}. ${item.instruction}`);
        });
      }

      if (results.setupGuide.connectivityIssues.length > 0) {
        console.log("\n  🌐 Connectivity Issues:");
        results.setupGuide.connectivityIssues.forEach((item, index) => {
          console.log(`    ${index + 1}. ${item.service}: ${item.error}`);
          if (item.troubleshooting.length > 0) {
            item.troubleshooting.forEach((tip) => {
              console.log(`       - ${tip}`);
            });
          }
        });
      }

      console.log("\n  🚀 Quick Start:");
      results.setupGuide.quickStart.forEach((step) => {
        console.log(`    ${step}`);
      });
    }

    console.log("\n" + "=".repeat(60));
  }

  /**
   * Static method for quick validation
   */
  static async quickValidate(options = {}) {
    const validator = new ConfigValidator(options);
    const results = await validator.validate();
    validator.printResults();
    return results;
  }
}

export default ConfigValidator;
