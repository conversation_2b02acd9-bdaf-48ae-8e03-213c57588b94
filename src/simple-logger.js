/**
 * 简化的日志系统 - 只显示重要信息
 */

const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
};

const COLORS = {
  ERROR: "\x1b[31m",
  WARN: "\x1b[33m",
  INFO: "\x1b[32m",
  DEBUG: "\x1b[36m",
  RESET: "\x1b[0m",
};

class SimpleLogger {
  constructor(component = "App") {
    this.component = component;
    this.level = process.env.LOG_LEVEL || "INFO";
    this.minLevel = LOG_LEVELS[this.level] || LOG_LEVELS.INFO;
  }

  log(level, message, data = {}) {
    if (LOG_LEVELS[level] > this.minLevel) return;

    const timestamp = new Date().toISOString().substring(11, 19);
    const color = COLORS[level] || "";
    const reset = COLORS.RESET;

    let output = `${color}[${timestamp}] ${level.padEnd(5)} ${
      this.component
    }: ${message}${reset}`;

    // 只在有重要数据时显示
    if (data && Object.keys(data).length > 0 && level !== "DEBUG") {
      const importantData = {};
      // 只显示重要字段
      if (data.port) importantData.port = data.port;
      if (data.url) importantData.url = data.url;
      if (data.error) importantData.error = data.error;
      if (data.status) importantData.status = data.status;

      if (Object.keys(importantData).length > 0) {
        output += ` ${JSON.stringify(importantData)}`;
      }
    }

    console.log(output);
  }

  error(message, data = {}) {
    this.log("ERROR", message, data);
  }

  warn(message, data = {}) {
    this.log("WARN", message, data);
  }

  info(message, data = {}) {
    this.log("INFO", message, data);
  }

  debug(message, data = {}) {
    this.log("DEBUG", message, data);
  }
}

export function createSimpleLogger(component) {
  return new SimpleLogger(component);
}

export { SimpleLogger };
