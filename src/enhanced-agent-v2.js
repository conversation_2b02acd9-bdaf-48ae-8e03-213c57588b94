#!/usr/bin/env node

/**
 * Enhanced LiveKit Agents + Deepgram Transcription Agent v2
 * Addresses beta version issues, implements robust fallback mechanisms,
 * and provides comprehensive status reporting and metrics collection
 */

import { WorkerOptions, cli, defineAgent, multimodal } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import * as openai from "@livekit/agents-plugin-openai";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";
import { AudioStreamProcessor } from "./audio-stream-processor.js";
import { ConfigValidator } from "./config-validator.js";
import {
  ErrorHandler,
  ErrorTypes,
  ErrorSeverity,
  TranscriptionError,
  createComponentErrorHandler,
} from "./error-handler.js";

config();

// Enhanced logging utility with correlation IDs and structured logging
export class StructuredLogger {
  constructor(component = "EnhancedAgent") {
    this.component = component;
    this.correlationId = this.generateCorrelationId();
  }

  generateCorrelationId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  log(level, message, data = {}, correlationId = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      component: this.component,
      correlationId: correlationId || this.correlationId,
      message,
      ...data,
    };

    // Console output for development
    const colorMap = {
      ERROR: "\x1b[31m",
      WARN: "\x1b[33m",
      INFO: "\x1b[36m",
      DEBUG: "\x1b[37m",
      RESET: "\x1b[0m",
    };

    const color = colorMap[level.toUpperCase()] || colorMap.RESET;
    console.log(
      `${color}[${timestamp}] ${level.toUpperCase()}: ${message}${
        colorMap.RESET
      }`,
      Object.keys(data).length > 0 ? JSON.stringify(data, null, 2) : ""
    );

    // Structured JSON logging for production
    if (process.env.NODE_ENV === "production") {
      console.log(JSON.stringify(logEntry));
    }
  }

  info(message, data = {}) {
    this.log("info", message, data);
  }
  warn(message, data = {}) {
    this.log("warn", message, data);
  }
  error(message, data = {}) {
    this.log("error", message, data);
  }
  debug(message, data = {}) {
    this.log("debug", message, data);
  }

  // Create child logger with same correlation ID
  child(component) {
    const child = new StructuredLogger(component);
    child.correlationId = this.correlationId;
    return child;
  }
}

// Enhanced configuration validator with sampleRate workarounds
export class EnhancedConfigValidator {
  static async validate() {
    const logger = new StructuredLogger("ConfigValidator");
    logger.info("🔍 Starting comprehensive configuration validation...");

    try {
      const validator = new ConfigValidator({
        timeout: 10000,
        retries: 2,
        skipConnectivityTests: process.env.SKIP_CONNECTIVITY_TESTS === "true",
      });

      const results = await validator.validate();

      // Check for known beta version issues
      await this.checkBetaVersionIssues(logger);

      if (results.overall) {
        logger.info("✅ Configuration validation passed", {
          envVarsValid: Object.keys(results.envVars).length,
          connectivityTests: Object.keys(results.connectivity).length,
          warnings: results.warnings.length,
        });
      } else {
        logger.error("❌ Configuration validation failed", {
          errors: results.errors.length,
          warnings: results.warnings.length,
          setupGuideAvailable: !!results.setupGuide,
        });
      }

      // Log warnings and errors
      results.warnings.forEach((warning) => {
        logger.warn(`⚠️ ${warning.message}`, {
          type: warning.type,
          impact: warning.impact,
        });
      });

      results.errors.forEach((error) => {
        logger.error(`❌ ${error.message}`, {
          type: error.type,
          variable: error.variable,
        });
      });

      return results.overall;
    } catch (error) {
      logger.error("❌ Configuration validation error", {
        error: error.message,
        stack: error.stack,
      });
      return false;
    }
  }

  static async checkBetaVersionIssues(logger) {
    logger.info("🔍 Checking for known beta version issues...");

    // Check for sampleRate configuration issues
    const sampleRateIssues = [];

    // Check if we're using a beta version that has sampleRate issues
    try {
      // Try to read version from environment or use a simpler check
      const version = process.env.LIVEKIT_AGENTS_VERSION || "unknown";

      if (
        version.includes("beta") ||
        version.includes("alpha") ||
        version.includes("rc")
      ) {
        logger.warn("⚠️ Using beta/alpha version of LiveKit Agents", {
          version,
          potentialIssues: [
            "sampleRate configuration",
            "MultimodalAgent stability",
          ],
        });
        sampleRateIssues.push("Beta version detected");
      } else {
        logger.debug("LiveKit Agents version check", { version });
      }
    } catch (error) {
      logger.debug("Could not determine LiveKit Agents version", {
        error: error.message,
      });
    }

    // Check for common sampleRate configuration problems
    const commonSampleRates = [16000, 24000, 48000];
    const recommendedSampleRate = 16000; // Deepgram's preferred rate

    logger.info("📊 Audio configuration recommendations", {
      recommendedSampleRate,
      supportedRates: commonSampleRates,
      workarounds: [
        "Use 16000 Hz for best Deepgram compatibility",
        "Avoid sampleRate configuration in beta versions",
        "Use direct STT stream as fallback",
      ],
    });

    return sampleRateIssues;
  }
}

// Enhanced metrics collector
export class MetricsCollector {
  constructor() {
    this.metrics = {
      // Agent metrics
      agentInitializations: 0,
      agentInitializationFailures: 0,
      agentFallbacks: 0,
      currentAgentType: null,

      // Connection metrics
      connectionsAttempted: 0,
      connectionsSuccessful: 0,
      connectionsFailed: 0,
      averageConnectionTime: 0,

      // Transcription metrics
      transcriptionsProcessed: 0,
      interimResults: 0,
      finalResults: 0,
      averageConfidence: 0,

      // Error metrics
      errorsTotal: 0,
      errorsByType: {},
      errorsBySeverity: {},

      // Performance metrics
      memoryUsage: 0,
      cpuUsage: 0,
      uptime: 0,

      // Audio processing metrics
      audioTracksProcessed: 0,
      audioProcessingErrors: 0,
      audioStrategiesUsed: {},

      // Timestamps
      startTime: Date.now(),
      lastActivity: Date.now(),
    };

    // Start periodic metrics collection
    this.startPeriodicCollection();
  }

  startPeriodicCollection() {
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000); // Every 30 seconds
  }

  collectSystemMetrics() {
    if (process.memoryUsage) {
      const memory = process.memoryUsage();
      this.metrics.memoryUsage = memory.heapUsed;
    }

    this.metrics.uptime = Date.now() - this.metrics.startTime;
    this.metrics.lastActivity = Date.now();
  }

  incrementCounter(metric, value = 1) {
    if (this.metrics.hasOwnProperty(metric)) {
      this.metrics[metric] += value;
    }
  }

  updateGauge(metric, value) {
    if (this.metrics.hasOwnProperty(metric)) {
      this.metrics[metric] = value;
    }
  }

  recordError(errorType, severity) {
    this.metrics.errorsTotal++;
    this.metrics.errorsByType[errorType] =
      (this.metrics.errorsByType[errorType] || 0) + 1;
    this.metrics.errorsBySeverity[severity] =
      (this.metrics.errorsBySeverity[severity] || 0) + 1;
  }

  recordTranscription(confidence, isFinal) {
    this.metrics.transcriptionsProcessed++;
    if (isFinal) {
      this.metrics.finalResults++;
    } else {
      this.metrics.interimResults++;
    }

    // Update average confidence
    const totalConfidence =
      this.metrics.averageConfidence *
        (this.metrics.transcriptionsProcessed - 1) +
      confidence;
    this.metrics.averageConfidence =
      totalConfidence / this.metrics.transcriptionsProcessed;
  }

  recordConnection(success, duration) {
    this.metrics.connectionsAttempted++;
    if (success) {
      this.metrics.connectionsSuccessful++;

      // Update average connection time
      const totalTime =
        this.metrics.averageConnectionTime *
          (this.metrics.connectionsSuccessful - 1) +
        duration;
      this.metrics.averageConnectionTime =
        totalTime / this.metrics.connectionsSuccessful;
    } else {
      this.metrics.connectionsFailed++;
    }
  }

  getMetrics() {
    return { ...this.metrics };
  }

  getPrometheusMetrics() {
    const metrics = this.getMetrics();
    let output = "";

    // Counter metrics
    output += `# HELP agent_initializations_total Total number of agent initializations\n`;
    output += `# TYPE agent_initializations_total counter\n`;
    output += `agent_initializations_total ${metrics.agentInitializations}\n\n`;

    output += `# HELP connections_total Total number of connection attempts\n`;
    output += `# TYPE connections_total counter\n`;
    output += `connections_total{status="attempted"} ${metrics.connectionsAttempted}\n`;
    output += `connections_total{status="successful"} ${metrics.connectionsSuccessful}\n`;
    output += `connections_total{status="failed"} ${metrics.connectionsFailed}\n\n`;

    output += `# HELP transcriptions_total Total number of transcriptions processed\n`;
    output += `# TYPE transcriptions_total counter\n`;
    output += `transcriptions_total{type="interim"} ${metrics.interimResults}\n`;
    output += `transcriptions_total{type="final"} ${metrics.finalResults}\n\n`;

    // Gauge metrics
    output += `# HELP agent_uptime_seconds Agent uptime in seconds\n`;
    output += `# TYPE agent_uptime_seconds gauge\n`;
    output += `agent_uptime_seconds ${Math.floor(metrics.uptime / 1000)}\n\n`;

    output += `# HELP memory_usage_bytes Current memory usage in bytes\n`;
    output += `# TYPE memory_usage_bytes gauge\n`;
    output += `memory_usage_bytes ${metrics.memoryUsage}\n\n`;

    return output;
  }
}

// Enhanced agent manager with comprehensive fallback mechanisms
export class EnhancedAgentManager {
  constructor() {
    this.logger = new StructuredLogger("AgentManager");
    this.metrics = new MetricsCollector();
    this.errorHandler = createComponentErrorHandler("AgentManager");

    // Agent instances
    this.primaryAgent = null;
    this.fallbackAgents = [];
    this.currentAgentType = null;
    this.audioProcessor = null;

    // Status tracking
    this.status = {
      status: "initializing",
      agentType: null,
      lastActivity: Date.now(),
      errorDetails: null,
      initializationAttempts: 0,
      fallbacksUsed: 0,
    };

    // Agent configuration strategies
    this.agentStrategies = [
      {
        name: "multimodal",
        priority: 1,
        description: "Full MultimodalAgent with STT, LLM, and TTS",
        requirements: ["DEEPGRAM_API_KEY", "OPENAI_API_KEY"],
        fallbackOnError: true,
      },
      {
        name: "multimodal-stt-only",
        priority: 2,
        description: "MultimodalAgent with STT only (no LLM/TTS)",
        requirements: ["DEEPGRAM_API_KEY"],
        fallbackOnError: true,
      },
      {
        name: "direct-stt",
        priority: 3,
        description: "Direct STT stream without MultimodalAgent",
        requirements: ["DEEPGRAM_API_KEY"],
        fallbackOnError: false,
      },
    ];
  }

  async initialize() {
    this.logger.info("🚀 Initializing Enhanced Agent Manager v2");
    this.metrics.incrementCounter("agentInitializations");
    this.status.initializationAttempts++;

    // Try each strategy in order of priority
    for (const strategy of this.agentStrategies) {
      this.logger.info(`🎯 Attempting strategy: ${strategy.name}`, {
        description: strategy.description,
        requirements: strategy.requirements,
      });

      try {
        const success = await this.initializeWithStrategy(strategy);
        if (success) {
          this.currentAgentType = strategy.name;
          this.status.status = "connected";
          this.status.agentType = strategy.name;
          this.status.errorDetails = null;
          this.metrics.updateGauge("currentAgentType", strategy.name);

          this.logger.info(
            `✅ Successfully initialized with strategy: ${strategy.name}`
          );
          return true;
        }
      } catch (error) {
        this.logger.warn(`⚠️ Strategy ${strategy.name} failed`, {
          error: error.message,
          willTryFallback: strategy.fallbackOnError,
        });

        this.metrics.recordError(
          ErrorTypes.AGENT_INIT_FAILED,
          ErrorSeverity.MEDIUM
        );

        if (!strategy.fallbackOnError) {
          break;
        }
      }
    }

    // All strategies failed
    const finalError = new TranscriptionError(
      ErrorTypes.AGENT_INIT_FAILED,
      "All agent initialization strategies failed",
      {
        severity: ErrorSeverity.CRITICAL,
        context: {
          strategiesAttempted: this.agentStrategies.map((s) => s.name),
          initializationAttempts: this.status.initializationAttempts,
        },
        retryable: false,
        userMessage: "转录服务初始化失败，请检查配置或联系管理员",
        suggestions: [
          "检查所有必需的环境变量是否已设置",
          "验证 API 密钥是否有效",
          "检查网络连接",
          "查看详细日志以获取更多信息",
          "联系技术支持",
        ],
      }
    );

    await this.errorHandler.handle(finalError, {
      operation: "initialize",
      isFinal: true,
    });

    this.status.status = "error";
    this.status.errorDetails = finalError.toJSON();
    this.metrics.incrementCounter("agentInitializationFailures");

    return false;
  }

  async initializeWithStrategy(strategy) {
    const strategyLogger = this.logger.child(`Strategy-${strategy.name}`);

    // Check requirements
    const missingRequirements = strategy.requirements.filter(
      (req) => !process.env[req]
    );
    if (missingRequirements.length > 0) {
      throw new Error(
        `Missing required environment variables: ${missingRequirements.join(
          ", "
        )}`
      );
    }

    switch (strategy.name) {
      case "multimodal":
        return await this.initializeMultimodalAgent(strategyLogger, true);

      case "multimodal-stt-only":
        return await this.initializeMultimodalAgent(strategyLogger, false);

      case "direct-stt":
        return await this.initializeDirectSTTAgent(strategyLogger);

      default:
        throw new Error(`Unknown strategy: ${strategy.name}`);
    }
  }

  async initializeMultimodalAgent(logger, includeOpenAI = true) {
    logger.debug("Attempting MultimodalAgent initialization", {
      includeOpenAI,
    });

    // Create STT with enhanced configuration and sampleRate workaround
    const sttConfig = {
      model: "nova-2",
      language: "multi",
      smartFormat: true,
      interimResults: true,
      punctuate: true,
      diarize: true,
    };

    // Avoid sampleRate configuration in beta versions
    if (!this.isBetaVersion()) {
      sttConfig.sampleRate = 16000; // Deepgram's preferred rate
    } else {
      logger.warn("⚠️ Skipping sampleRate configuration due to beta version");
    }

    const stt = new deepgram.STT(sttConfig);

    let llm = null;
    let tts = null;

    if (includeOpenAI && process.env.OPENAI_API_KEY) {
      try {
        llm = new openai.LLM({
          model: "gpt-4o-mini",
        });

        tts = new openai.TTS({
          model: "tts-1",
          voice: "alloy",
        });

        logger.debug("✅ OpenAI components initialized");
      } catch (error) {
        logger.warn("⚠️ OpenAI initialization failed", {
          error: error.message,
        });
        if (includeOpenAI) {
          throw error; // Fail if OpenAI was required
        }
      }
    } else {
      logger.info("ℹ️ Using STT-only configuration");
      // Create minimal mock implementations for MultimodalAgent compatibility
      llm = { model: "mock", generate: async () => ({ text: "" }) };
      tts = { model: "mock", synthesize: async () => null };
    }

    // Create MultimodalAgent with error handling
    try {
      this.primaryAgent = new multimodal.MultimodalAgent({
        stt: stt,
        llm: llm,
        tts: tts,
      });

      logger.debug("✅ MultimodalAgent instance created");
    } catch (error) {
      // Handle known beta version issues
      if (
        error.message.includes("sampleRate") ||
        error.message.includes("undefined")
      ) {
        logger.warn("⚠️ Beta version issue detected, attempting workaround", {
          error: error.message,
        });

        // Try without sampleRate configuration
        const fallbackSTT = new deepgram.STT({
          model: "nova-2",
          language: "multi",
          smartFormat: true,
          interimResults: true,
          punctuate: true,
          diarize: true,
          // Explicitly omit sampleRate
        });

        this.primaryAgent = new multimodal.MultimodalAgent({
          stt: fallbackSTT,
          llm: llm,
          tts: tts,
        });

        logger.info("✅ MultimodalAgent created with beta version workaround");
      } else {
        throw error;
      }
    }

    // Initialize AudioStreamProcessor with enhanced options
    this.audioProcessor = new AudioStreamProcessor(stt, {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      connectionTimeout: 10000,
    });

    this.setupAudioProcessorEventHandlers();
    logger.debug("✅ AudioStreamProcessor initialized");

    return true;
  }

  async initializeDirectSTTAgent(logger) {
    logger.debug("Attempting Direct STT Agent initialization");

    const sttConfig = {
      model: "nova-2",
      language: "multi",
      smartFormat: true,
      interimResults: true,
      punctuate: true,
      diarize: true,
    };

    // Use recommended sampleRate for direct STT
    if (!this.isBetaVersion()) {
      sttConfig.sampleRate = 16000;
    }

    const stt = new deepgram.STT(sttConfig);

    // Initialize AudioStreamProcessor
    this.audioProcessor = new AudioStreamProcessor(stt, {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      connectionTimeout: 10000,
    });

    this.setupAudioProcessorEventHandlers();
    logger.debug("✅ Direct STT Agent initialized");

    return true;
  }

  isBetaVersion() {
    // Simple heuristic to detect beta versions
    try {
      const version = process.env.LIVEKIT_AGENTS_VERSION || "unknown";
      return (
        version.includes("beta") ||
        version.includes("alpha") ||
        version.includes("rc")
      );
    } catch {
      return false;
    }
  }

  setupAudioProcessorEventHandlers() {
    if (!this.audioProcessor) return;

    const logger = this.logger.child("AudioProcessor");

    // Connection events
    this.audioProcessor.on("connectionAttempt", (data) => {
      logger.info("🔗 Audio connection attempt started", data);
      this.metrics.incrementCounter("connectionsAttempted");
    });

    this.audioProcessor.on("connectionSuccess", (data) => {
      logger.info(`✅ Audio connection successful using ${data.strategy}`, {
        connectionTime: data.connectionTime,
        participant: data.participant,
        trackId: data.trackId,
      });

      this.metrics.recordConnection(true, data.connectionTime);
      this.metrics.incrementCounter("audioTracksProcessed");

      if (data.strategy) {
        this.metrics.metrics.audioStrategiesUsed[data.strategy] =
          (this.metrics.metrics.audioStrategiesUsed[data.strategy] || 0) + 1;
      }
    });

    this.audioProcessor.on("connectionFailed", (data) => {
      logger.error("❌ All audio connection strategies failed", data);
      this.metrics.recordConnection(false, 0);
      this.metrics.incrementCounter("audioProcessingErrors");
      this.metrics.recordError(
        ErrorTypes.AUDIO_TRACK_FAILED,
        ErrorSeverity.HIGH
      );
    });

    // Transcription events
    this.audioProcessor.on("transcriptionBroadcast", (data) => {
      logger.info(`📝 Transcription broadcasted: "${data.text}"`, {
        isFinal: data.isFinal,
        participant: data.participant,
        trackId: data.trackId,
      });

      this.metrics.recordTranscription(0.9, data.isFinal); // Default confidence
    });

    // Error events
    this.audioProcessor.on("transcriptionBroadcastError", (data) => {
      logger.error("❌ Failed to broadcast transcription", {
        error: data.error,
        text: data.text,
        participant: data.participant,
        trackId: data.trackId,
      });

      this.metrics.recordError(
        ErrorTypes.STT_SERVICE_ERROR,
        ErrorSeverity.MEDIUM
      );
    });

    // Handle multimodal agent requests
    this.audioProcessor.on("multimodalAgentRequest", async (data) => {
      if (this.currentAgentType?.includes("multimodal") && this.primaryAgent) {
        try {
          logger.debug("Delegating to MultimodalAgent for audio processing");
          data.callback(true);
        } catch (error) {
          logger.error("MultimodalAgent audio processing failed", {
            error: error.message,
          });
          data.callback(false);
        }
      } else {
        data.callback(false);
      }
    });
  }

  async handleParticipant(ctx, participant) {
    const logger = this.logger.child("ParticipantHandler");
    logger.info("👤 Handling new participant", {
      identity: participant.identity,
      agentType: this.currentAgentType,
    });

    this.metrics.incrementCounter("connectionsAttempted");

    if (this.currentAgentType?.includes("multimodal") && this.primaryAgent) {
      try {
        logger.debug("Starting MultimodalAgent session");
        const session = await this.primaryAgent.start(ctx.room, participant);

        this.setupMultimodalEventHandlers(session, ctx, participant, logger);
        logger.info("✅ MultimodalAgent session started successfully");

        this.metrics.incrementCounter("connectionsSuccessful");
        return session;
      } catch (error) {
        logger.error("❌ MultimodalAgent session failed, falling back", {
          error: error.message,
        });

        this.currentAgentType = "direct-stt";
        this.status.agentType = "direct-stt";
        this.status.status = "fallback";
        this.status.fallbacksUsed++;
        this.metrics.incrementCounter("agentFallbacks");
      }
    }

    // Handle with direct STT approach
    if (
      this.currentAgentType === "direct-stt" ||
      this.currentAgentType?.includes("stt")
    ) {
      this.setupDirectSTTHandling(ctx, participant, logger);
      logger.info("✅ Direct STT handling setup complete");
      this.metrics.incrementCounter("connectionsSuccessful");
    }
  }

  setupMultimodalEventHandlers(session, ctx, participant, logger) {
    session.on("user_speech_committed", async (event) => {
      logger.info(`📝 Final transcription: "${event.text}"`, {
        confidence: event.confidence,
        participant: participant.identity,
      });

      this.metrics.recordTranscription(event.confidence || 0.9, true);

      const transcriptionData = {
        type: "transcription",
        text: event.text,
        confidence: event.confidence || 0.9,
        isFinal: true,
        timestamp: Date.now(),
        participant: participant.identity,
        metadata: {
          agentType: this.currentAgentType,
          processingTime: Date.now(),
          correlationId: logger.correlationId,
        },
      };

      try {
        await ctx.room.localParticipant.publishData(
          JSON.stringify(transcriptionData),
          { reliable: true }
        );
      } catch (error) {
        logger.error("❌ Failed to broadcast final transcription", {
          error: error.message,
        });
        this.metrics.recordError(
          ErrorTypes.STT_SERVICE_ERROR,
          ErrorSeverity.MEDIUM
        );
      }
    });

    session.on("user_speech_interim", async (event) => {
      if (event.text && event.text.trim()) {
        logger.debug(`📝 Interim transcription: "${event.text}"`);

        this.metrics.recordTranscription(event.confidence || 0.8, false);

        const transcriptionData = {
          type: "transcription",
          text: event.text,
          confidence: event.confidence || 0.8,
          isFinal: false,
          timestamp: Date.now(),
          participant: participant.identity,
          metadata: {
            agentType: this.currentAgentType,
            processingTime: Date.now(),
            correlationId: logger.correlationId,
          },
        };

        try {
          await ctx.room.localParticipant.publishData(
            JSON.stringify(transcriptionData),
            { reliable: true }
          );
        } catch (error) {
          logger.error("❌ Failed to broadcast interim transcription", {
            error: error.message,
          });
        }
      }
    });

    session.on("error", (error) => {
      logger.error("❌ MultimodalAgent session error", {
        error: error.message,
        stack: error.stack,
      });

      this.status.status = "error";
      this.status.errorDetails = {
        code: "SESSION_ERROR",
        message: error.message,
        correlationId: logger.correlationId,
      };

      this.metrics.recordError(
        ErrorTypes.AGENT_CRASHED,
        ErrorSeverity.CRITICAL
      );
    });
  }

  setupDirectSTTHandling(ctx, participant, logger) {
    ctx.room.on(
      "trackSubscribed",
      async (track, publication, trackParticipant) => {
        if (track.kind === "audio" || track.kind === 1) {
          logger.info("🎵 Audio track subscribed", {
            trackId: track.info?.sid,
            participant: trackParticipant.identity,
          });

          if (this.audioProcessor) {
            try {
              const success = await this.audioProcessor.connectTrack(
                track,
                ctx,
                trackParticipant
              );

              if (success) {
                logger.info("✅ Audio track connected successfully", {
                  trackId: track.info?.sid,
                  participant: trackParticipant.identity,
                  strategy: this.audioProcessor.currentStrategy,
                });
              } else {
                logger.error("❌ Failed to connect audio track", {
                  trackId: track.info?.sid,
                  participant: trackParticipant.identity,
                });
              }
            } catch (error) {
              logger.error("❌ Audio track connection error", {
                error: error.message,
                trackId: track.info?.sid,
                participant: trackParticipant.identity,
              });
            }
          }
        }
      }
    );
  }

  getStatus() {
    const audioProcessorMetrics = this.audioProcessor
      ? this.audioProcessor.getMetrics()
      : {};

    return {
      ...this.status,
      lastActivity: Date.now(),
      audioProcessor: audioProcessorMetrics,
      metrics: this.metrics.getMetrics(),
      correlationId: this.logger.correlationId,
    };
  }

  getMetrics() {
    return this.metrics.getMetrics();
  }

  getPrometheusMetrics() {
    return this.metrics.getPrometheusMetrics();
  }
}

// Main agent definition with comprehensive error handling
export default defineAgent({
  entry: async (ctx) => {
    const logger = new StructuredLogger("MainAgent");
    logger.info(
      "🚀 Starting Enhanced LiveKit Agents + Deepgram Transcription v2"
    );

    // 简化配置验证 - 只检查必需的环境变量
    const requiredVars = [
      "LIVEKIT_URL",
      "LIVEKIT_API_KEY",
      "LIVEKIT_API_SECRET",
      "DEEPGRAM_API_KEY",
    ];
    const missingVars = requiredVars.filter((varName) => !process.env[varName]);

    if (missingVars.length > 0) {
      logger.error(
        `❌ Missing required environment variables: ${missingVars.join(", ")}`
      );
      logger.error(
        "Please check your .env file and ensure all required variables are set."
      );
      process.exit(1);
    }

    logger.info("✅ Basic configuration validation passed");

    try {
      // Connect to room
      await ctx.connect();
      logger.info(`📍 Connected to room: ${ctx.room.name}`);

      // Initialize enhanced agent manager
      const agentManager = new EnhancedAgentManager();
      const initialized = await agentManager.initialize();

      if (!initialized) {
        logger.error("❌ Failed to initialize any agent type");
        process.exit(1);
      }

      // Wait for participant
      logger.info("⏳ Waiting for participant to join...");
      const participant = await ctx.waitForParticipant();
      logger.info(`👤 Participant joined: ${participant.identity}`);

      // Handle participant with enhanced agent manager
      await agentManager.handleParticipant(ctx, participant);

      // Set up status and metrics reporting
      setInterval(() => {
        const status = agentManager.getStatus();
        logger.debug("📊 Agent status", {
          status: status.status,
          agentType: status.agentType,
          metrics: status.metrics,
        });
      }, 30000); // Report every 30 seconds

      // Set up metrics endpoint (if running in server mode)
      if (process.env.ENABLE_METRICS_ENDPOINT === "true") {
        // This would be handled by the main server, but we can log metrics
        setInterval(() => {
          const metrics = agentManager.getPrometheusMetrics();
          logger.debug("📈 Prometheus metrics", { metrics });
        }, 60000); // Every minute
      }

      logger.info(
        "🎯 Enhanced Agent v2 fully operational, waiting for audio input..."
      );
    } catch (error) {
      logger.error("❌ Agent startup failed", {
        error: error.message,
        stack: error.stack,
      });
      process.exit(1);
    }
  },
});

// Enhanced startup with comprehensive error handling
if (import.meta.url === `file://${process.argv[1]}`) {
  const logger = new StructuredLogger("Startup");

  process.on("unhandledRejection", (reason, promise) => {
    logger.error("Unhandled Promise Rejection", {
      reason: reason?.message || reason,
      stack: reason?.stack,
      promise: promise.toString(),
    });
  });

  process.on("uncaughtException", (error) => {
    logger.error("Uncaught Exception", {
      error: error.message,
      stack: error.stack,
    });
    process.exit(1);
  });

  // Graceful shutdown handling
  process.on("SIGTERM", () => {
    logger.info("Received SIGTERM, shutting down gracefully...");
    process.exit(0);
  });

  process.on("SIGINT", () => {
    logger.info("Received SIGINT, shutting down gracefully...");
    process.exit(0);
  });

  // Start the worker
  try {
    cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
  } catch (error) {
    logger.error("Failed to start worker", {
      error: error.message,
      stack: error.stack,
    });
    process.exit(1);
  }
}
