/**
 * Metrics Collection System
 * Provides Prometheus-compatible metrics for monitoring
 */

class MetricsCollector {
  constructor() {
    this.metrics = {
      // Connection metrics
      connections_total: 0,
      connections_active: 0,
      connections_failed: 0,

      // Transcription metrics
      transcriptions_total: 0,
      transcriptions_interim: 0,
      transcriptions_final: 0,
      transcription_latency_ms: [],
      transcription_confidence: [],

      // Agent metrics
      agent_status: "initializing", // initializing, connected, processing, error
      agent_restarts: 0,
      agent_errors: 0,

      // System metrics
      memory_usage_mb: 0,
      cpu_usage_percent: 0,
      uptime_seconds: 0,

      // API metrics
      deepgram_requests: 0,
      deepgram_errors: 0,
      livekit_connections: 0,
      livekit_errors: 0,

      // Performance metrics
      audio_processing_time_ms: [],
      websocket_latency_ms: [],

      // Error tracking
      errors_by_type: {},
      last_error_timestamp: null,

      // Custom metrics
      rooms_active: 0,
      participants_total: 0,
      speakers_detected: 0,
    };

    this.startTime = Date.now();
    this.lastMetricsUpdate = Date.now();

    // Start periodic system metrics collection
    this.startSystemMetricsCollection();
  }

  // Connection metrics
  incrementConnections() {
    this.metrics.connections_total++;
    this.metrics.connections_active++;
  }

  decrementActiveConnections() {
    this.metrics.connections_active = Math.max(
      0,
      this.metrics.connections_active - 1
    );
  }

  incrementFailedConnections() {
    this.metrics.connections_failed++;
  }

  // Transcription metrics
  recordTranscription(type, confidence, latency) {
    this.metrics.transcriptions_total++;

    if (type === "interim") {
      this.metrics.transcriptions_interim++;
    } else if (type === "final") {
      this.metrics.transcriptions_final++;
    }

    if (confidence !== undefined) {
      this.metrics.transcription_confidence.push(confidence);
      // Keep only last 1000 values
      if (this.metrics.transcription_confidence.length > 1000) {
        this.metrics.transcription_confidence =
          this.metrics.transcription_confidence.slice(-1000);
      }
    }

    if (latency !== undefined) {
      this.metrics.transcription_latency_ms.push(latency);
      // Keep only last 1000 values
      if (this.metrics.transcription_latency_ms.length > 1000) {
        this.metrics.transcription_latency_ms =
          this.metrics.transcription_latency_ms.slice(-1000);
      }
    }
  }

  // Agent metrics
  setAgentStatus(status) {
    this.metrics.agent_status = status;
  }

  incrementAgentRestarts() {
    this.metrics.agent_restarts++;
  }

  incrementAgentErrors() {
    this.metrics.agent_errors++;
  }

  // API metrics
  recordDeepgramRequest(success = true) {
    this.metrics.deepgram_requests++;
    if (!success) {
      this.metrics.deepgram_errors++;
    }
  }

  recordLiveKitConnection(success = true) {
    this.metrics.livekit_connections++;
    if (!success) {
      this.metrics.livekit_errors++;
    }
  }

  // Performance metrics
  recordAudioProcessingTime(timeMs) {
    this.metrics.audio_processing_time_ms.push(timeMs);
    // Keep only last 1000 values
    if (this.metrics.audio_processing_time_ms.length > 1000) {
      this.metrics.audio_processing_time_ms =
        this.metrics.audio_processing_time_ms.slice(-1000);
    }
  }

  recordWebSocketLatency(latencyMs) {
    this.metrics.websocket_latency_ms.push(latencyMs);
    // Keep only last 1000 values
    if (this.metrics.websocket_latency_ms.length > 1000) {
      this.metrics.websocket_latency_ms =
        this.metrics.websocket_latency_ms.slice(-1000);
    }
  }

  // Error tracking
  recordError(errorType, errorMessage) {
    if (!this.metrics.errors_by_type[errorType]) {
      this.metrics.errors_by_type[errorType] = 0;
    }
    this.metrics.errors_by_type[errorType]++;
    this.metrics.last_error_timestamp = Date.now();
  }

  // Room and participant metrics
  setActiveRooms(count) {
    this.metrics.rooms_active = count;
  }

  setTotalParticipants(count) {
    this.metrics.participants_total = count;
  }

  setSpeakersDetected(count) {
    this.metrics.speakers_detected = count;
  }

  // System metrics collection
  startSystemMetricsCollection() {
    setInterval(() => {
      this.updateSystemMetrics();
    }, 30000); // Update every 30 seconds
  }

  updateSystemMetrics() {
    // Memory usage
    const memUsage = process.memoryUsage();
    this.metrics.memory_usage_mb = Math.round(memUsage.rss / 1024 / 1024);

    // Uptime
    this.metrics.uptime_seconds = Math.round(
      (Date.now() - this.startTime) / 1000
    );

    // CPU usage (simplified - would need more complex calculation for accurate CPU usage)
    this.metrics.cpu_usage_percent = Math.round(
      process.cpuUsage().user / 1000000
    ); // Rough approximation

    this.lastMetricsUpdate = Date.now();
  }

  // Calculate statistical metrics
  calculateStats(values) {
    if (values.length === 0) return { avg: 0, min: 0, max: 0, p95: 0, p99: 0 };

    const sorted = [...values].sort((a, b) => a - b);
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const min = sorted[0];
    const max = sorted[sorted.length - 1];
    const p95 = sorted[Math.floor(sorted.length * 0.95)];
    const p99 = sorted[Math.floor(sorted.length * 0.99)];

    return { avg, min, max, p95, p99 };
  }

  // Get Prometheus-formatted metrics
  getPrometheusMetrics() {
    const timestamp = Date.now();
    const lines = [];

    // Connection metrics
    lines.push(
      `# HELP transcription_connections_total Total number of connections`
    );
    lines.push(`# TYPE transcription_connections_total counter`);
    lines.push(
      `transcription_connections_total ${this.metrics.connections_total} ${timestamp}`
    );

    lines.push(
      `# HELP transcription_connections_active Current active connections`
    );
    lines.push(`# TYPE transcription_connections_active gauge`);
    lines.push(
      `transcription_connections_active ${this.metrics.connections_active} ${timestamp}`
    );

    lines.push(
      `# HELP transcription_connections_failed Total failed connections`
    );
    lines.push(`# TYPE transcription_connections_failed counter`);
    lines.push(
      `transcription_connections_failed ${this.metrics.connections_failed} ${timestamp}`
    );

    // Transcription metrics
    lines.push(
      `# HELP transcription_requests_total Total transcription requests`
    );
    lines.push(`# TYPE transcription_requests_total counter`);
    lines.push(
      `transcription_requests_total ${this.metrics.transcriptions_total} ${timestamp}`
    );

    lines.push(
      `# HELP transcription_interim_total Total interim transcriptions`
    );
    lines.push(`# TYPE transcription_interim_total counter`);
    lines.push(
      `transcription_interim_total ${this.metrics.transcriptions_interim} ${timestamp}`
    );

    lines.push(`# HELP transcription_final_total Total final transcriptions`);
    lines.push(`# TYPE transcription_final_total counter`);
    lines.push(
      `transcription_final_total ${this.metrics.transcriptions_final} ${timestamp}`
    );

    // Transcription latency statistics
    const latencyStats = this.calculateStats(
      this.metrics.transcription_latency_ms
    );
    lines.push(
      `# HELP transcription_latency_ms Transcription latency in milliseconds`
    );
    lines.push(`# TYPE transcription_latency_ms summary`);
    lines.push(
      `transcription_latency_ms{quantile="0.5"} ${latencyStats.avg} ${timestamp}`
    );
    lines.push(
      `transcription_latency_ms{quantile="0.95"} ${latencyStats.p95} ${timestamp}`
    );
    lines.push(
      `transcription_latency_ms{quantile="0.99"} ${latencyStats.p99} ${timestamp}`
    );

    // Confidence statistics
    const confidenceStats = this.calculateStats(
      this.metrics.transcription_confidence
    );
    lines.push(
      `# HELP transcription_confidence Transcription confidence score`
    );
    lines.push(`# TYPE transcription_confidence summary`);
    lines.push(
      `transcription_confidence{quantile="0.5"} ${confidenceStats.avg} ${timestamp}`
    );
    lines.push(
      `transcription_confidence{quantile="0.95"} ${confidenceStats.p95} ${timestamp}`
    );
    lines.push(
      `transcription_confidence{quantile="0.99"} ${confidenceStats.p99} ${timestamp}`
    );

    // Agent metrics
    lines.push(
      `# HELP transcription_agent_status Agent status (0=initializing, 1=connected, 2=processing, 3=error)`
    );
    lines.push(`# TYPE transcription_agent_status gauge`);
    const statusValue =
      { initializing: 0, connected: 1, processing: 2, error: 3 }[
        this.metrics.agent_status
      ] || 0;
    lines.push(`transcription_agent_status ${statusValue} ${timestamp}`);

    lines.push(
      `# HELP transcription_agent_restarts_total Total agent restarts`
    );
    lines.push(`# TYPE transcription_agent_restarts_total counter`);
    lines.push(
      `transcription_agent_restarts_total ${this.metrics.agent_restarts} ${timestamp}`
    );

    lines.push(`# HELP transcription_agent_errors_total Total agent errors`);
    lines.push(`# TYPE transcription_agent_errors_total counter`);
    lines.push(
      `transcription_agent_errors_total ${this.metrics.agent_errors} ${timestamp}`
    );

    // System metrics
    lines.push(`# HELP transcription_memory_usage_mb Memory usage in MB`);
    lines.push(`# TYPE transcription_memory_usage_mb gauge`);
    lines.push(
      `transcription_memory_usage_mb ${this.metrics.memory_usage_mb} ${timestamp}`
    );

    lines.push(`# HELP transcription_uptime_seconds Uptime in seconds`);
    lines.push(`# TYPE transcription_uptime_seconds gauge`);
    lines.push(
      `transcription_uptime_seconds ${this.metrics.uptime_seconds} ${timestamp}`
    );

    // API metrics
    lines.push(
      `# HELP transcription_deepgram_requests_total Total Deepgram API requests`
    );
    lines.push(`# TYPE transcription_deepgram_requests_total counter`);
    lines.push(
      `transcription_deepgram_requests_total ${this.metrics.deepgram_requests} ${timestamp}`
    );

    lines.push(
      `# HELP transcription_deepgram_errors_total Total Deepgram API errors`
    );
    lines.push(`# TYPE transcription_deepgram_errors_total counter`);
    lines.push(
      `transcription_deepgram_errors_total ${this.metrics.deepgram_errors} ${timestamp}`
    );

    lines.push(
      `# HELP transcription_livekit_connections_total Total LiveKit connections`
    );
    lines.push(`# TYPE transcription_livekit_connections_total counter`);
    lines.push(
      `transcription_livekit_connections_total ${this.metrics.livekit_connections} ${timestamp}`
    );

    lines.push(
      `# HELP transcription_livekit_errors_total Total LiveKit errors`
    );
    lines.push(`# TYPE transcription_livekit_errors_total counter`);
    lines.push(
      `transcription_livekit_errors_total ${this.metrics.livekit_errors} ${timestamp}`
    );

    // Room and participant metrics
    lines.push(`# HELP transcription_rooms_active Active rooms`);
    lines.push(`# TYPE transcription_rooms_active gauge`);
    lines.push(
      `transcription_rooms_active ${this.metrics.rooms_active} ${timestamp}`
    );

    lines.push(`# HELP transcription_participants_total Total participants`);
    lines.push(`# TYPE transcription_participants_total gauge`);
    lines.push(
      `transcription_participants_total ${this.metrics.participants_total} ${timestamp}`
    );

    lines.push(`# HELP transcription_speakers_detected Detected speakers`);
    lines.push(`# TYPE transcription_speakers_detected gauge`);
    lines.push(
      `transcription_speakers_detected ${this.metrics.speakers_detected} ${timestamp}`
    );

    // Error metrics by type
    for (const [errorType, count] of Object.entries(
      this.metrics.errors_by_type
    )) {
      lines.push(`# HELP transcription_errors_by_type_total Errors by type`);
      lines.push(`# TYPE transcription_errors_by_type_total counter`);
      lines.push(
        `transcription_errors_by_type_total{type="${errorType}"} ${count} ${timestamp}`
      );
    }

    return lines.join("\n") + "\n";
  }

  // Get JSON metrics for health checks
  getHealthMetrics() {
    return {
      status: this.metrics.agent_status,
      uptime_seconds: this.metrics.uptime_seconds,
      memory_usage_mb: this.metrics.memory_usage_mb,
      connections_active: this.metrics.connections_active,
      transcriptions_total: this.metrics.transcriptions_total,
      last_activity: this.lastMetricsUpdate,
      errors: {
        agent_errors: this.metrics.agent_errors,
        deepgram_errors: this.metrics.deepgram_errors,
        livekit_errors: this.metrics.livekit_errors,
        last_error: this.metrics.last_error_timestamp,
      },
      performance: {
        avg_transcription_latency: this.calculateStats(
          this.metrics.transcription_latency_ms
        ).avg,
        avg_confidence: this.calculateStats(
          this.metrics.transcription_confidence
        ).avg,
      },
    };
  }

  // Reset metrics (useful for testing)
  reset() {
    this.metrics = {
      connections_total: 0,
      connections_active: 0,
      connections_failed: 0,
      transcriptions_total: 0,
      transcriptions_interim: 0,
      transcriptions_final: 0,
      transcription_latency_ms: [],
      transcription_confidence: [],
      agent_status: "initializing",
      agent_restarts: 0,
      agent_errors: 0,
      memory_usage_mb: 0,
      cpu_usage_percent: 0,
      uptime_seconds: 0,
      deepgram_requests: 0,
      deepgram_errors: 0,
      livekit_connections: 0,
      livekit_errors: 0,
      audio_processing_time_ms: [],
      websocket_latency_ms: [],
      errors_by_type: {},
      last_error_timestamp: null,
      rooms_active: 0,
      participants_total: 0,
      speakers_detected: 0,
    };
    this.startTime = Date.now();
  }
}

// Singleton instance
const metricsCollector = new MetricsCollector();

export default metricsCollector;
