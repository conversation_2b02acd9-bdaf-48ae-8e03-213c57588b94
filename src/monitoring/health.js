/**
 * Health Check System
 * Provides comprehensive health monitoring for all system components
 */

import metricsCollector from "./metrics.js";
import fs from "fs";
import path from "path";

class HealthChecker {
  constructor() {
    this.checks = new Map();
    this.lastHealthCheck = null;
    this.healthHistory = [];
    this.maxHistorySize = 100;

    // Register default health checks
    this.registerDefaultChecks();
  }

  // Register a health check
  registerCheck(name, checkFunction, options = {}) {
    this.checks.set(name, {
      name,
      check: checkFunction,
      timeout: options.timeout || 5000,
      critical: options.critical || false,
      description: options.description || `Health check for ${name}`,
      lastResult: null,
      lastRun: null,
    });
  }

  // Remove a health check
  unregisterCheck(name) {
    this.checks.delete(name);
  }

  // Register default system health checks
  registerDefaultChecks() {
    // Memory usage check
    this.registerCheck(
      "memory",
      async () => {
        const memUsage = process.memoryUsage();
        const memUsageMB = Math.round(memUsage.rss / 1024 / 1024);
        const memLimit = parseInt(process.env.MEMORY_LIMIT_MB) || 1024;

        const healthy = memUsageMB < memLimit * 0.9; // 90% threshold

        return {
          healthy,
          message: healthy
            ? `Memory usage: ${memUsageMB}MB (${Math.round(
                (memUsageMB / memLimit) * 100
              )}%)`
            : `High memory usage: ${memUsageMB}MB (${Math.round(
                (memUsageMB / memLimit) * 100
              )}%)`,
          data: {
            usage_mb: memUsageMB,
            limit_mb: memLimit,
            percentage: Math.round((memUsageMB / memLimit) * 100),
          },
        };
      },
      { critical: true, description: "System memory usage" }
    );

    // Process uptime check
    this.registerCheck(
      "uptime",
      async () => {
        const uptimeSeconds = process.uptime();
        const healthy = uptimeSeconds > 10; // At least 10 seconds uptime

        return {
          healthy,
          message: `Process uptime: ${Math.round(uptimeSeconds)}s`,
          data: {
            uptime_seconds: Math.round(uptimeSeconds),
            started_at: new Date(
              Date.now() - uptimeSeconds * 1000
            ).toISOString(),
          },
        };
      },
      { description: "Process uptime" }
    );

    // Environment variables check
    this.registerCheck(
      "environment",
      async () => {
        const requiredVars = [
          "LIVEKIT_URL",
          "LIVEKIT_API_KEY",
          "LIVEKIT_API_SECRET",
          "DEEPGRAM_API_KEY",
        ];

        const missing = requiredVars.filter((varName) => !process.env[varName]);
        const healthy = missing.length === 0;

        return {
          healthy,
          message: healthy
            ? "All required environment variables are set"
            : `Missing environment variables: ${missing.join(", ")}`,
          data: {
            required_vars: requiredVars,
            missing_vars: missing,
            optional_vars: {
              openai_configured: !!process.env.OPENAI_API_KEY,
            },
          },
        };
      },
      { critical: true, description: "Environment configuration" }
    );

    // Disk space check (if applicable)
    this.registerCheck(
      "disk",
      async () => {
        try {
          const stats = fs.statSync(".");
          // This is a simplified check - in production you'd want to check actual disk usage
          const healthy = true; // Placeholder

          return {
            healthy,
            message: "Disk space check passed",
            data: {
              working_directory: process.cwd(),
              writable: true,
            },
          };
        } catch (error) {
          return {
            healthy: false,
            message: `Disk check failed: ${error.message}`,
            data: { error: error.message },
          };
        }
      },
      { description: "Disk space and accessibility" }
    );

    // Agent status check
    this.registerCheck(
      "agent",
      async () => {
        const metrics = metricsCollector.getHealthMetrics();
        const healthy =
          metrics.status !== "error" &&
          Date.now() - metrics.last_activity < 300000; // 5 minutes

        return {
          healthy,
          message: healthy
            ? `Agent status: ${metrics.status}`
            : `Agent unhealthy: ${metrics.status}`,
          data: {
            status: metrics.status,
            last_activity: new Date(metrics.last_activity).toISOString(),
            uptime_seconds: metrics.uptime_seconds,
            errors: metrics.errors,
          },
        };
      },
      { critical: true, description: "LiveKit Agent status" }
    );

    // Metrics collection check
    this.registerCheck(
      "metrics",
      async () => {
        const metrics = metricsCollector.getHealthMetrics();
        const healthy = metrics.uptime_seconds > 0;

        return {
          healthy,
          message: healthy
            ? "Metrics collection active"
            : "Metrics collection inactive",
          data: {
            connections_active: metrics.connections_active,
            transcriptions_total: metrics.transcriptions_total,
            memory_usage_mb: metrics.memory_usage_mb,
          },
        };
      },
      { description: "Metrics collection system" }
    );
  }

  // Run a single health check
  async runCheck(checkName) {
    const checkConfig = this.checks.get(checkName);
    if (!checkConfig) {
      throw new Error(`Health check '${checkName}' not found`);
    }

    const startTime = Date.now();

    try {
      // Run check with timeout
      const result = await Promise.race([
        checkConfig.check(),
        new Promise((_, reject) =>
          setTimeout(
            () => reject(new Error("Health check timeout")),
            checkConfig.timeout
          )
        ),
      ]);

      const duration = Date.now() - startTime;

      const checkResult = {
        name: checkName,
        healthy: result.healthy,
        message: result.message,
        data: result.data || {},
        duration_ms: duration,
        timestamp: new Date().toISOString(),
        critical: checkConfig.critical,
      };

      checkConfig.lastResult = checkResult;
      checkConfig.lastRun = Date.now();

      return checkResult;
    } catch (error) {
      const duration = Date.now() - startTime;

      const checkResult = {
        name: checkName,
        healthy: false,
        message: `Health check failed: ${error.message}`,
        data: { error: error.message },
        duration_ms: duration,
        timestamp: new Date().toISOString(),
        critical: checkConfig.critical,
      };

      checkConfig.lastResult = checkResult;
      checkConfig.lastRun = Date.now();

      return checkResult;
    }
  }

  // Run all health checks
  async runAllChecks() {
    const startTime = Date.now();
    const results = [];
    const errors = [];

    // Run all checks in parallel
    const checkPromises = Array.from(this.checks.keys()).map(
      async (checkName) => {
        try {
          const result = await this.runCheck(checkName);
          results.push(result);

          if (!result.healthy && result.critical) {
            errors.push(
              `Critical check failed: ${result.name} - ${result.message}`
            );
          }
        } catch (error) {
          const failedResult = {
            name: checkName,
            healthy: false,
            message: `Check execution failed: ${error.message}`,
            data: { error: error.message },
            duration_ms: 0,
            timestamp: new Date().toISOString(),
            critical: this.checks.get(checkName)?.critical || false,
          };

          results.push(failedResult);
          errors.push(
            `Check execution failed: ${checkName} - ${error.message}`
          );
        }
      }
    );

    await Promise.all(checkPromises);

    const totalDuration = Date.now() - startTime;
    const healthyChecks = results.filter((r) => r.healthy).length;
    const totalChecks = results.length;
    const criticalFailures = results.filter(
      (r) => !r.healthy && r.critical
    ).length;

    const overallHealthy = criticalFailures === 0;

    const healthReport = {
      healthy: overallHealthy,
      timestamp: new Date().toISOString(),
      duration_ms: totalDuration,
      summary: {
        total_checks: totalChecks,
        healthy_checks: healthyChecks,
        failed_checks: totalChecks - healthyChecks,
        critical_failures: criticalFailures,
      },
      checks: results,
      errors: errors.length > 0 ? errors : undefined,
    };

    // Store in history
    this.healthHistory.push(healthReport);
    if (this.healthHistory.length > this.maxHistorySize) {
      this.healthHistory = this.healthHistory.slice(-this.maxHistorySize);
    }

    this.lastHealthCheck = healthReport;
    return healthReport;
  }

  // Get the last health check result
  getLastHealthCheck() {
    return this.lastHealthCheck;
  }

  // Get health check history
  getHealthHistory(limit = 10) {
    return this.healthHistory.slice(-limit);
  }

  // Get a simple health status
  async getSimpleHealth() {
    const lastCheck = this.lastHealthCheck;

    // If no recent check, run a quick one
    if (
      !lastCheck ||
      Date.now() - new Date(lastCheck.timestamp).getTime() > 60000
    ) {
      await this.runAllChecks();
    }

    return {
      status: this.lastHealthCheck.healthy ? "ok" : "error",
      timestamp: this.lastHealthCheck.timestamp,
      checks_passed: this.lastHealthCheck.summary.healthy_checks,
      checks_total: this.lastHealthCheck.summary.total_checks,
      critical_failures: this.lastHealthCheck.summary.critical_failures,
    };
  }

  // Get detailed health information
  async getDetailedHealth() {
    await this.runAllChecks();
    return this.lastHealthCheck;
  }

  // Check if system is ready (all critical checks pass)
  async isReady() {
    const health = await this.getDetailedHealth();
    return health.healthy && health.summary.critical_failures === 0;
  }

  // Check if system is alive (basic liveness check)
  async isAlive() {
    try {
      // Run only critical checks for liveness
      const criticalChecks = Array.from(this.checks.entries())
        .filter(([_, config]) => config.critical)
        .map(([name, _]) => name);

      const results = await Promise.all(
        criticalChecks.map((checkName) => this.runCheck(checkName))
      );

      return results.every((result) => result.healthy);
    } catch (error) {
      return false;
    }
  }

  // Export health data for external monitoring
  exportHealthData() {
    return {
      current_health: this.lastHealthCheck,
      registered_checks: Array.from(this.checks.keys()),
      history_size: this.healthHistory.length,
      system_info: {
        node_version: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
        uptime: process.uptime(),
      },
    };
  }
}

// Singleton instance
const healthChecker = new HealthChecker();

export default healthChecker;
