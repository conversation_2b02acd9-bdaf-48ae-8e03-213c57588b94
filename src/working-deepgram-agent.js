#!/usr/bin/env node

/**
 * 调试代理 - 检查音频轨道处理
 */

import { WorkerOptions, cli, defineAgent } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 简单的调试代理
export default defineAgent({
  entry: async (ctx) => {
    console.log("🚀 调试代理启动");

    // 连接到房间
    await ctx.connect();
    console.log(`📍 已连接到房间: ${ctx.room.name}`);

    // 等待参与者
    console.log("⏳ 等待参与者加入...");
    const participant = await ctx.waitForParticipant();
    console.log(`👤 参与者加入: ${participant.identity}`);

    // 自动订阅所有轨道
    ctx.room.on("trackPublished", async (publication, participant) => {
      console.log("📢 轨道发布，尝试订阅:", {
        kind: publication.kind,
        trackName: publication.trackName,
        participant: participant.identity,
      });

      if (publication.kind === "audio") {
        try {
          await publication.setSubscribed(true);
          console.log("✅ 已订阅音频轨道");
        } catch (error) {
          console.error("❌ 订阅轨道失败:", error.message);
        }
      }
    });

    // 监听所有轨道事件
    ctx.room.on("trackPublished", (publication, participant) => {
      console.log("📢 轨道发布:", {
        kind: publication.kind,
        trackName: publication.trackName,
        participant: participant.identity,
      });
    });

    ctx.room.on(
      "trackSubscribed",
      async (track, publication, trackParticipant) => {
        console.log("🎵 轨道订阅:", {
          kind: track.kind,
          trackId: track.info?.sid,
          participant: trackParticipant.identity,
        });

        if (track.kind === "audio" || track.kind === 1) {
          console.log("🎤 检测到音频轨道!");

          try {
            // 创建STT
            const stt = deepgram.STT({
              model: "nova-2",
              language: "en",
              smart_format: true,
              interim_results: true,
            });

            console.log("🔗 创建STT成功，开始连接音频流...");

            // 连接音频流
            const sttStream = stt.stream();

            // 监听转录结果
            sttStream.on("data", async (event) => {
              if (event.text && event.text.trim()) {
                console.log(
                  `📝 转录结果: "${event.text}" (final: ${event.is_final})`
                );

                // 广播转录数据
                const transcriptionData = {
                  type: "transcription",
                  isFinal: event.is_final,
                  text: event.text,
                  confidence: event.confidence || 0.9,
                  timestamp: Date.now(),
                  participant: trackParticipant.identity,
                };

                try {
                  await ctx.room.localParticipant.publishData(
                    JSON.stringify(transcriptionData),
                    { reliable: true }
                  );
                  console.log("✅ 转录数据已广播");
                } catch (error) {
                  console.error("❌ 广播失败:", error.message);
                }
              }
            });

            // 连接音频轨道到STT
            sttStream.input.put(track);
            console.log("✅ 音频轨道已连接到STT流");
          } catch (error) {
            console.error("❌ 音频处理错误:", error.message);
          }
        }
      }
    );

    console.log("✅ 调试代理设置完成，等待音频轨道...");
  },
});

// 启动代理
if (import.meta.url === `file://${process.argv[1]}`) {
  cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
}
