/**
 * AudioStreamProcessor - Robust audio stream connection with multiple strategies
 * Implements retry logic with exponential backoff for connection failures
 */

import { EventEmitter } from "events";
import {
  ErrorTypes,
  ErrorSeverity,
  TranscriptionError,
  createComponentErrorHandler,
} from "./error-handler.js";

export class AudioStreamProcessor extends EventEmitter {
  constructor(stt, options = {}) {
    super();
    this.stt = stt;
    this.options = {
      maxRetries: 3,
      baseDelay: 1000, // 1 second
      maxDelay: 30000, // 30 seconds
      backoffMultiplier: 2,
      connectionTimeout: 10000, // 10 seconds
      ...options,
    };

    // Connection strategies in order of preference
    this.connectionStrategies = [
      "multimodalAgent",
      "directSTTStream",
      "manualAudioExtraction",
      "fallbackBuffering",
    ];

    this.currentStrategy = null;
    this.isConnected = false;
    this.retryCount = 0;
    this.connectionAttempts = new Map();
    this.activeStreams = new Map();
    this.errorHandler = createComponentErrorHandler("AudioStreamProcessor");

    // Metrics tracking
    this.metrics = {
      totalConnections: 0,
      successfulConnections: 0,
      failedConnections: 0,
      retriesUsed: 0,
      averageConnectionTime: 0,
      strategiesUsed: new Map(),
      errorsHandled: 0,
      criticalErrors: 0,
    };
  }

  /**
   * Main entry point for connecting an audio track
   */
  async connectTrack(track, ctx, participant) {
    const startTime = Date.now();
    const trackId = track.info?.sid || `track_${Date.now()}`;

    this.emit("connectionAttempt", {
      trackId,
      participant: participant.identity,
      strategies: this.connectionStrategies,
    });

    this.metrics.totalConnections++;

    // Use error handler for connection attempts
    const result = await this.errorHandler.handleWithRetry(
      new TranscriptionError(
        ErrorTypes.AUDIO_TRACK_FAILED,
        "Attempting audio track connection",
        {
          severity: ErrorSeverity.HIGH,
          context: { trackId, participant: participant.identity },
          userMessage: "正在连接音频流...",
          suggestions: [
            "确保麦克风权限已授予",
            "检查音频设备是否正常工作",
            "尝试刷新页面重新连接",
          ],
        }
      ),
      async () => {
        return await this._attemptConnectionWithRetry(
          track,
          ctx,
          participant,
          trackId
        );
      },
      {
        operation: "connectTrack",
        trackId,
        participant: participant.identity,
      }
    );

    if (result.success) {
      this.metrics.successfulConnections++;
      this.metrics.retriesUsed += result.retriesUsed || 0;

      const connectionTime = Date.now() - startTime;
      this._updateAverageConnectionTime(connectionTime);

      this.emit("connectionSuccess", {
        trackId,
        strategy: this.currentStrategy,
        connectionTime,
        participant: participant.identity,
        retriesUsed: result.retriesUsed || 0,
      });

      return true;
    } else {
      this.metrics.failedConnections++;
      this.metrics.errorsHandled++;

      if (result.error?.severity === ErrorSeverity.CRITICAL) {
        this.metrics.criticalErrors++;
      }

      // Create comprehensive failure error
      const failureError = new TranscriptionError(
        ErrorTypes.AUDIO_TRACK_FAILED,
        "All audio connection strategies failed",
        {
          severity: ErrorSeverity.HIGH,
          context: {
            trackId,
            participant: participant.identity,
            strategiesTried: this.connectionStrategies,
            totalRetries: result.retriesUsed || 0,
          },
          userMessage: "音频连接失败，无法进行转录",
          suggestions: [
            "检查麦克风权限设置",
            "确认音频设备正常工作",
            "尝试使用不同的浏览器",
            "刷新页面重新尝试",
            "联系技术支持",
          ],
          retryable: true,
        }
      );

      await this.errorHandler.handle(failureError, {
        operation: "connectTrack",
        trackId,
        isFinal: true,
      });

      this.emit("connectionFailed", {
        trackId,
        participant: participant.identity,
        allStrategiesFailed: true,
        error: failureError.toJSON(),
      });

      return false;
    }
  }

  /**
   * Attempt connection with retry logic and exponential backoff
   */
  async _attemptConnectionWithRetry(track, ctx, participant, trackId) {
    for (const strategy of this.connectionStrategies) {
      this.retryCount = 0;

      while (this.retryCount <= this.options.maxRetries) {
        try {
          this.emit("strategyAttempt", {
            strategy,
            attempt: this.retryCount + 1,
            maxAttempts: this.options.maxRetries + 1,
            trackId,
          });

          const success = await this._tryConnectionStrategy(
            strategy,
            track,
            ctx,
            participant,
            trackId
          );

          if (success) {
            this.currentStrategy = strategy;
            this.isConnected = true;

            // Update strategy usage metrics
            const currentCount = this.metrics.strategiesUsed.get(strategy) || 0;
            this.metrics.strategiesUsed.set(strategy, currentCount + 1);

            return true;
          }
        } catch (error) {
          this.emit("strategyError", {
            strategy,
            attempt: this.retryCount + 1,
            error: error.message,
            trackId,
          });

          // If this isn't the last retry, wait before trying again
          if (this.retryCount < this.options.maxRetries) {
            const delay = this._calculateBackoffDelay();
            this.emit("retryDelay", { strategy, delay, trackId });
            await this._sleep(delay);
          }
        }

        this.retryCount++;
        this.metrics.retriesUsed++;
      }

      // Strategy failed after all retries, try next strategy
      this.emit("strategyFailed", {
        strategy,
        totalAttempts: this.retryCount,
        trackId,
      });
    }

    return false;
  }

  /**
   * Try a specific connection strategy with timeout
   */
  async _tryConnectionStrategy(strategy, track, ctx, participant, trackId) {
    return new Promise(async (resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(
          new Error(
            `Connection timeout after ${this.options.connectionTimeout}ms`
          )
        );
      }, this.options.connectionTimeout);

      try {
        let result = false;

        switch (strategy) {
          case "multimodalAgent":
            result = await this._tryMultimodalAgent(
              track,
              ctx,
              participant,
              trackId
            );
            break;
          case "directSTTStream":
            result = await this._tryDirectSTTStream(
              track,
              ctx,
              participant,
              trackId
            );
            break;
          case "manualAudioExtraction":
            result = await this._tryManualAudioExtraction(
              track,
              ctx,
              participant,
              trackId
            );
            break;
          case "fallbackBuffering":
            result = await this._tryFallbackBuffering(
              track,
              ctx,
              participant,
              trackId
            );
            break;
          default:
            throw new Error(`Unknown strategy: ${strategy}`);
        }

        clearTimeout(timeout);
        resolve(result);
      } catch (error) {
        clearTimeout(timeout);
        reject(error);
      }
    });
  }

  /**
   * Strategy 1: MultimodalAgent approach (requires external agent manager)
   */
  async _tryMultimodalAgent(track, ctx, participant, trackId) {
    this.emit("strategyDetails", {
      strategy: "multimodalAgent",
      message: "Attempting MultimodalAgent connection",
      trackId,
    });

    // This strategy should be handled by an external AgentManager
    // We'll emit an event for the manager to handle
    return new Promise((resolve) => {
      this.emit("multimodalAgentRequest", {
        track,
        ctx,
        participant,
        trackId,
        callback: (success) => resolve(success),
      });

      // If no external handler responds within 2 seconds, consider it failed
      setTimeout(() => resolve(false), 2000);
    });
  }

  /**
   * Strategy 2: Direct STT Stream connection
   */
  async _tryDirectSTTStream(track, ctx, participant, trackId) {
    this.emit("strategyDetails", {
      strategy: "directSTTStream",
      message: "Attempting direct STT stream connection",
      trackId,
    });

    if (!this.stt) {
      throw new Error("STT instance not available");
    }

    const sttStream = this.stt.stream();

    if (!sttStream || !sttStream.input) {
      throw new Error("STT stream or input not available");
    }

    // Try different input methods
    const inputMethods = [
      "put",
      "push",
      "write",
      "send",
      "pushTrack",
      "addTrack",
    ];

    for (const method of inputMethods) {
      if (typeof sttStream.input[method] === "function") {
        try {
          this.emit("strategyDetails", {
            strategy: "directSTTStream",
            message: `Trying input method: ${method}`,
            trackId,
          });

          await sttStream.input[method](track);

          // Set up output handling
          this._setupSTTOutputHandling(sttStream, ctx, participant, trackId);

          // Store active stream for cleanup
          this.activeStreams.set(trackId, sttStream);

          this.emit("strategyDetails", {
            strategy: "directSTTStream",
            message: `Successfully connected using ${method}`,
            trackId,
          });

          return true;
        } catch (error) {
          this.emit("strategyDetails", {
            strategy: "directSTTStream",
            message: `Input method ${method} failed: ${error.message}`,
            trackId,
          });
        }
      }
    }

    throw new Error("No working input method found for direct STT stream");
  }

  /**
   * Strategy 3: Manual audio extraction from ffi_handle
   */
  async _tryManualAudioExtraction(track, ctx, participant, trackId) {
    this.emit("strategyDetails", {
      strategy: "manualAudioExtraction",
      message: "Attempting manual audio extraction",
      trackId,
    });

    if (!track.ffi_handle) {
      throw new Error("No ffi_handle available for manual extraction");
    }

    // Create STT stream
    const sttStream = this.stt.stream();

    // Set up output handling first
    this._setupSTTOutputHandling(sttStream, ctx, participant, trackId);

    // Attempt to extract audio data from ffi_handle
    try {
      // This is a placeholder for the actual ffi_handle audio extraction
      // The implementation would depend on LiveKit's internal audio handling
      const audioData = await this._extractAudioFromFFIHandle(track.ffi_handle);

      if (audioData && sttStream.input) {
        // Try to feed the extracted audio data
        if (typeof sttStream.input.write === "function") {
          sttStream.input.write(audioData);
        } else if (typeof sttStream.input.push === "function") {
          sttStream.input.push(audioData);
        } else {
          throw new Error("No suitable method to feed extracted audio data");
        }

        this.activeStreams.set(trackId, sttStream);

        this.emit("strategyDetails", {
          strategy: "manualAudioExtraction",
          message: "Successfully extracted and fed audio data",
          trackId,
        });

        return true;
      }
    } catch (error) {
      throw new Error(`Manual audio extraction failed: ${error.message}`);
    }

    throw new Error(
      "Manual audio extraction completed but no data was processed"
    );
  }

  /**
   * Strategy 4: Fallback buffering approach
   */
  async _tryFallbackBuffering(track, ctx, participant, trackId) {
    this.emit("strategyDetails", {
      strategy: "fallbackBuffering",
      message: "Attempting fallback buffering approach",
      trackId,
    });

    // Create a buffer to collect audio data
    const audioBuffer = [];
    const sttStream = this.stt.stream();

    // Set up output handling
    this._setupSTTOutputHandling(sttStream, ctx, participant, trackId);

    // Set up a periodic buffer flush
    const bufferFlushInterval = setInterval(() => {
      if (audioBuffer.length > 0 && sttStream.input) {
        try {
          const data = audioBuffer.splice(0); // Clear buffer and get data

          // Try to send buffered data
          if (typeof sttStream.input.write === "function") {
            for (const chunk of data) {
              sttStream.input.write(chunk);
            }
          }
        } catch (error) {
          this.emit("strategyDetails", {
            strategy: "fallbackBuffering",
            message: `Buffer flush error: ${error.message}`,
            trackId,
          });
        }
      }
    }, 100); // Flush every 100ms

    // Store cleanup function
    this.activeStreams.set(trackId, {
      stream: sttStream,
      cleanup: () => clearInterval(bufferFlushInterval),
    });

    this.emit("strategyDetails", {
      strategy: "fallbackBuffering",
      message: "Fallback buffering setup complete",
      trackId,
    });

    return true; // This strategy always "succeeds" as a last resort
  }

  /**
   * Extract audio data from ffi_handle (placeholder implementation)
   */
  async _extractAudioFromFFIHandle(ffiHandle) {
    // This is a placeholder for the actual implementation
    // The real implementation would depend on LiveKit's internal audio handling
    // and would require access to the native audio processing functions

    this.emit("strategyDetails", {
      strategy: "manualAudioExtraction",
      message: "Extracting audio from ffi_handle (placeholder implementation)",
    });

    // For now, we'll simulate the extraction process
    // In a real implementation, this would involve:
    // 1. Accessing the native audio buffer from ffi_handle
    // 2. Converting the audio format if necessary
    // 3. Returning the audio data in a format suitable for STT

    throw new Error(
      "Manual audio extraction not yet fully implemented - requires LiveKit internal API access"
    );
  }

  /**
   * Set up STT output handling with enhanced error handling
   */
  _setupSTTOutputHandling(sttStream, ctx, participant, trackId) {
    this.emit("strategyDetails", {
      message: "Setting up STT output handling",
      trackId,
    });

    (async () => {
      try {
        for await (const event of sttStream) {
          this.emit("transcriptionEvent", {
            trackId,
            hasText: !!event.text,
            isFinal: event.isFinal,
            confidence: event.confidence,
            participant: participant.identity,
          });

          if (event.text && event.text.trim()) {
            const transcriptionData = {
              type: "transcription",
              text: event.text.trim(),
              confidence: event.confidence || 0.9,
              isFinal: event.isFinal || false,
              timestamp: Date.now(),
              participant: participant.identity,
              metadata: {
                strategy: this.currentStrategy,
                trackId,
                processingTime: Date.now(),
              },
            };

            try {
              await ctx.room.localParticipant.publishData(
                JSON.stringify(transcriptionData),
                { reliable: true }
              );

              this.emit("transcriptionBroadcast", {
                trackId,
                text: event.text,
                isFinal: event.isFinal,
                participant: participant.identity,
              });
            } catch (error) {
              this.emit("transcriptionBroadcastError", {
                trackId,
                text: event.text,
                error: error.message,
                participant: participant.identity,
              });
            }
          }
        }
      } catch (error) {
        this.emit("sttStreamError", {
          trackId,
          error: error.message,
          stack: error.stack,
          participant: participant.identity,
        });

        // Attempt to reconnect if this was an unexpected error
        this._handleStreamError(error, trackId);
      }
    })();
  }

  /**
   * Handle STT stream errors with potential reconnection
   */
  _handleStreamError(error, trackId) {
    this.emit("streamErrorHandling", {
      trackId,
      error: error.message,
      willAttemptReconnect: this.retryCount < this.options.maxRetries,
    });

    // Mark as disconnected
    this.isConnected = false;

    // Clean up the failed stream
    this.cleanup(trackId);

    // Could implement automatic reconnection logic here
    // For now, we'll just emit the error and let the caller handle it
  }

  /**
   * Calculate exponential backoff delay
   */
  _calculateBackoffDelay() {
    const delay = Math.min(
      this.options.baseDelay *
        Math.pow(this.options.backoffMultiplier, this.retryCount),
      this.options.maxDelay
    );

    // Add some jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay;
    return Math.floor(delay + jitter);
  }

  /**
   * Update average connection time metric
   */
  _updateAverageConnectionTime(newTime) {
    const currentAvg = this.metrics.averageConnectionTime;
    const totalConnections = this.metrics.successfulConnections;

    this.metrics.averageConnectionTime =
      (currentAvg * (totalConnections - 1) + newTime) / totalConnections;
  }

  /**
   * Sleep utility for retry delays
   */
  _sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Clean up resources for a specific track
   */
  cleanup(trackId) {
    const stream = this.activeStreams.get(trackId);
    if (stream) {
      if (stream.cleanup && typeof stream.cleanup === "function") {
        stream.cleanup();
      }

      // Close stream if it has a close method
      if (stream.stream && typeof stream.stream.close === "function") {
        stream.stream.close();
      } else if (typeof stream.close === "function") {
        stream.close();
      }

      this.activeStreams.delete(trackId);

      this.emit("streamCleanup", { trackId });
    }
  }

  /**
   * Clean up all active streams
   */
  cleanupAll() {
    for (const [trackId] of this.activeStreams) {
      this.cleanup(trackId);
    }

    this.isConnected = false;
    this.currentStrategy = null;

    this.emit("allStreamsCleanup");
  }

  /**
   * Get current metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      isConnected: this.isConnected,
      currentStrategy: this.currentStrategy,
      activeStreams: this.activeStreams.size,
      strategiesUsed: Object.fromEntries(this.metrics.strategiesUsed),
    };
  }

  /**
   * Get current status
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      currentStrategy: this.currentStrategy,
      activeStreams: this.activeStreams.size,
      retryCount: this.retryCount,
      metrics: this.getMetrics(),
    };
  }
}

export default AudioStreamProcessor;
