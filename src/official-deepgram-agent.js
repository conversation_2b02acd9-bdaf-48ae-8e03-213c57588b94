#!/usr/bin/env node

/**
 * 基于debug-agent.js的成功模式，使用LiveKit Agents Deepgram插件
 */

import { WorkerOptions, cli, defineAgent } from "@livekit/agents";
import * as deepgram from "@livekit/agents-plugin-deepgram";
import { config } from "dotenv";
import { fileURLToPath } from "node:url";

config();

// 简单的调试代理
const agent = defineAgent({
  entry: async (ctx) => {
    console.log("� 官方Deepgram Agent启动");

    // 连接到房间
    await ctx.connect();
    console.log(`📍 已连接到房间: ${ctx.room.name}`);

    // 等待参与者
    console.log("⏳ 等待参与者加入...");
    const participant = await ctx.waitForParticipant();
    console.log(`� 参与者加入: ${participant.identity}`);

    // 自动订阅所有轨道
    ctx.room.on("trackPublished", async (publication, participant) => {
      console.log("📢 轨道发布，尝试订阅:", {
        kind: publication.kind,
        trackName: publication.trackName,
        participant: participant.identity,
      });

      if (publication.kind === "audio") {
        try {
          await publication.setSubscribed(true);
          console.log("✅ 已订阅音频轨道");
        } catch (error) {
          console.error("❌ 订阅轨道失败:", error.message);
        }
      }
    });

    // 监听轨道订阅事件
    ctx.room.on(
      "trackSubscribed",
      async (track, publication, trackParticipant) => {
        console.log("🎵 轨道订阅:", {
          kind: track.kind,
          trackId: track.info?.sid,
          participant: trackParticipant.identity,
        });

        if (track.kind === "audio" || track.kind === 1) {
          logger.info("🎤 检测到音频轨道，开始处理...");

          try {
            // 创建Deepgram客户端
            logger.info("🔧 创建Deepgram客户端...");
            const deepgramClient = createClient(process.env.DEEPGRAM_API_KEY);
            logger.info("✅ Deepgram客户端创建成功");

            // 创建实时转录连接
            logger.info("🔧 创建实时转录连接...");
            const connection = deepgramClient.listen.live({
              model: "nova-2-general",
              language: "en-US",
              smart_format: true,
              interim_results: true,
              endpointing: 300,
              vad_events: true,
            });
            logger.info("✅ 实时转录连接创建成功");

            // 设置事件监听器
            connection.on(LiveTranscriptionEvents.Open, () => {
              logger.info("🔗 Deepgram连接已打开");

              // 监听转录结果
              connection.on(LiveTranscriptionEvents.Transcript, (data) => {
                logger.info("📨 收到转录事件:", JSON.stringify(data, null, 2));

                if (
                  data.channel &&
                  data.channel.alternatives &&
                  data.channel.alternatives.length > 0
                ) {
                  const transcript = data.channel.alternatives[0].transcript;
                  if (transcript && transcript.trim()) {
                    const confidence =
                      data.channel.alternatives[0].confidence || 0.95;
                    const isFinal = data.is_final || false;

                    logger.info(
                      `📝 转录文本: "${transcript}" (final: ${isFinal}, confidence: ${confidence})`
                    );

                    // 发送转录结果
                    const transcriptionData = {
                      type: "transcription",
                      text: transcript,
                      timestamp: Date.now().toString(),
                      confidence: confidence,
                      is_final: isFinal,
                      participant: participant.identity,
                    };

                    ctx.room.localParticipant
                      .publishData(
                        new TextEncoder().encode(
                          JSON.stringify(transcriptionData)
                        ),
                        { reliable: true }
                      )
                      .then(() => {
                        logger.info(`✅ 转录数据发送成功: "${transcript}"`);
                      })
                      .catch((error) => {
                        logger.error("❌ 发送转录数据失败:", error.message);
                      });
                  }
                }
              });

              connection.on(LiveTranscriptionEvents.Metadata, (data) => {
                logger.info("📊 收到元数据:", JSON.stringify(data, null, 2));
              });

              connection.on(LiveTranscriptionEvents.Error, (error) => {
                logger.error("❌ Deepgram连接错误:", error);
              });

              connection.on(LiveTranscriptionEvents.Close, () => {
                logger.info("🔌 Deepgram连接已关闭");
              });

              // 创建AudioStream并开始发送音频数据
              logger.info("🔧 创建AudioStream...");
              const audioStream = new AudioStream(track);
              logger.info("✅ AudioStream创建成功");

              // 处理音频流
              const processAudio = async () => {
                try {
                  logger.info("🎵 开始处理音频流...");
                  let frameCount = 0;

                  for await (const audioEvent of audioStream) {
                    frameCount++;

                    // 发送音频数据到Deepgram
                    if (audioEvent.frame && audioEvent.frame.data) {
                      connection.send(audioEvent.frame.data);
                    }

                    // 每500帧记录一次进度
                    if (frameCount % 500 === 0) {
                      logger.info(`🎵 已处理 ${frameCount} 个音频帧`);
                    }
                  }
                } catch (error) {
                  logger.error("❌ 音频流处理失败:", error.message);
                }
              };

              // 启动音频处理
              processAudio();
            });
          } catch (error) {
            logger.error("❌ 处理音频轨道失败:", error.message);
            logger.error("🔍 错误堆栈:", error.stack);
          }
        }
      }
    );

    // 监听参与者断开连接
    ctx.room.on("participantDisconnected", (participant) => {
      logger.info(`👋 参与者断开连接: ${participant.identity}`);
    });

    logger.info("✅ OfficialDeepgramAgent 启动完成，等待音频输入...");
  },
});

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  cli.runApp(new WorkerOptions({ agent }));
}

export default agent;
