/**
 * Comprehensive Error Handling System
 * Provides structured error handling with descriptive messages and retry mechanisms
 */

import { EventEmitter } from "events";

// Error types and categories
export const ErrorTypes = {
  // Connection errors
  CONNECTION_FAILED: "CONNECTION_FAILED",
  CONNECTION_TIMEOUT: "CONNECTION_TIMEOUT",
  CONNECTION_LOST: "CONNECTION_LOST",

  // Authentication errors
  AUTH_FAILED: "AUTH_FAILED",
  TOKEN_EXPIRED: "TOKEN_EXPIRED",
  INVALID_CREDENTIALS: "INVALID_CREDENTIALS",

  // Configuration errors
  CONFIG_MISSING: "CONFIG_MISSING",
  CONFIG_INVALID: "CONFIG_INVALID",
  ENV_VAR_MISSING: "ENV_VAR_MISSING",

  // Audio processing errors
  AUDIO_TRACK_FAILED: "AUDIO_TRACK_FAILED",
  AUDIO_STREAM_ERROR: "AUDIO_STREAM_ERROR",
  MICROPHONE_ACCESS_DENIED: "MICROPHONE_ACCESS_DENIED",

  // STT service errors
  STT_SERVICE_ERROR: "STT_SERVICE_ERROR",
  STT_QUOTA_EXCEEDED: "STT_QUOTA_EXCEEDED",
  STT_UNSUPPORTED_FORMAT: "STT_UNSUPPORTED_FORMAT",

  // Agent errors
  AGENT_INIT_FAILED: "AGENT_INIT_FAILED",
  AGENT_CRASHED: "AGENT_CRASHED",
  AGENT_TIMEOUT: "AGENT_TIMEOUT",

  // Network errors
  NETWORK_ERROR: "NETWORK_ERROR",
  RATE_LIMITED: "RATE_LIMITED",
  SERVICE_UNAVAILABLE: "SERVICE_UNAVAILABLE",

  // Generic errors
  UNKNOWN_ERROR: "UNKNOWN_ERROR",
  VALIDATION_ERROR: "VALIDATION_ERROR",
  INTERNAL_ERROR: "INTERNAL_ERROR",
};

// Error severity levels
export const ErrorSeverity = {
  LOW: "low",
  MEDIUM: "medium",
  HIGH: "high",
  CRITICAL: "critical",
};

// Structured error class
export class TranscriptionError extends Error {
  constructor(type, message, options = {}) {
    super(message);
    this.name = "TranscriptionError";
    this.type = type;
    this.severity = options.severity || ErrorSeverity.MEDIUM;
    this.code = options.code || type;
    this.context = options.context || {};
    this.timestamp = new Date().toISOString();
    this.retryable = options.retryable !== false; // Default to retryable
    this.userMessage = options.userMessage || this.generateUserMessage();
    this.suggestions = options.suggestions || this.generateSuggestions();
    this.originalError = options.originalError;

    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, TranscriptionError);
    }
  }

  generateUserMessage() {
    const userMessages = {
      [ErrorTypes.CONNECTION_FAILED]: "无法连接到服务器，请检查网络连接",
      [ErrorTypes.CONNECTION_TIMEOUT]: "连接超时，请稍后重试",
      [ErrorTypes.CONNECTION_LOST]: "连接已断开，正在尝试重新连接",
      [ErrorTypes.AUTH_FAILED]: "身份验证失败，请检查配置",
      [ErrorTypes.TOKEN_EXPIRED]: "访问令牌已过期，正在刷新",
      [ErrorTypes.MICROPHONE_ACCESS_DENIED]: "需要麦克风权限才能进行转录",
      [ErrorTypes.AUDIO_TRACK_FAILED]: "音频处理失败，正在尝试其他方法",
      [ErrorTypes.STT_SERVICE_ERROR]: "语音识别服务暂时不可用",
      [ErrorTypes.STT_QUOTA_EXCEEDED]: "语音识别配额已用完，请稍后重试",
      [ErrorTypes.AGENT_INIT_FAILED]: "转录服务初始化失败",
      [ErrorTypes.CONFIG_MISSING]: "系统配置不完整，请联系管理员",
      [ErrorTypes.NETWORK_ERROR]: "网络连接异常，请检查网络设置",
      [ErrorTypes.RATE_LIMITED]: "请求过于频繁，请稍后重试",
      [ErrorTypes.SERVICE_UNAVAILABLE]: "服务暂时不可用，请稍后重试",
    };

    return userMessages[this.type] || "发生了未知错误，请稍后重试";
  }

  generateSuggestions() {
    const suggestions = {
      [ErrorTypes.CONNECTION_FAILED]: [
        "检查网络连接是否正常",
        "确认服务器地址配置正确",
        "尝试刷新页面重新连接",
      ],
      [ErrorTypes.MICROPHONE_ACCESS_DENIED]: [
        "点击浏览器地址栏的麦克风图标",
        "选择'允许'麦克风权限",
        "刷新页面重新尝试",
      ],
      [ErrorTypes.STT_QUOTA_EXCEEDED]: [
        "等待配额重置（通常在下一小时）",
        "联系管理员增加配额",
        "稍后重试",
      ],
      [ErrorTypes.CONFIG_MISSING]: [
        "检查环境变量配置",
        "确认 API 密钥设置正确",
        "联系系统管理员",
      ],
      [ErrorTypes.NETWORK_ERROR]: ["检查网络连接", "尝试切换网络", "稍后重试"],
    };

    return suggestions[this.type] || ["稍后重试", "联系技术支持"];
  }

  toJSON() {
    return {
      name: this.name,
      type: this.type,
      message: this.message,
      userMessage: this.userMessage,
      severity: this.severity,
      code: this.code,
      context: this.context,
      timestamp: this.timestamp,
      retryable: this.retryable,
      suggestions: this.suggestions,
      stack: this.stack,
    };
  }
}

// Error handler class with retry mechanisms
export class ErrorHandler extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      enableLogging: true,
      enableUserNotifications: true,
      ...options,
    };

    this.retryAttempts = new Map();
    this.errorHistory = [];
    this.maxHistorySize = 100;
  }

  /**
   * Handle an error with automatic retry logic
   */
  async handleError(error, context = {}, retryFunction = null) {
    const transcriptionError = this.normalizeError(error, context);

    // Log the error
    this.logError(transcriptionError);

    // Add to history
    this.addToHistory(transcriptionError);

    // Emit error event
    this.emit("error", transcriptionError);

    // Handle retry logic if function provided and error is retryable
    if (retryFunction && transcriptionError.retryable) {
      return await this.attemptRetry(
        transcriptionError,
        retryFunction,
        context
      );
    }

    return { success: false, error: transcriptionError };
  }

  /**
   * Normalize any error into a TranscriptionError
   */
  normalizeError(error, context = {}) {
    if (error instanceof TranscriptionError) {
      return error;
    }

    // Handle string errors
    if (typeof error === "string") {
      return new TranscriptionError(ErrorTypes.UNKNOWN_ERROR, error, {
        context,
        severity: ErrorSeverity.MEDIUM,
      });
    }

    // Detect error type based on error message and context
    const errorType = this.detectErrorType(error, context);

    return new TranscriptionError(errorType, error.message || "Unknown error", {
      context,
      originalError: error,
      severity: this.determineSeverity(errorType, context),
    });
  }

  /**
   * Detect error type from error message and context
   */
  detectErrorType(error, context) {
    const message = (error.message || "").toLowerCase();
    const contextType = context.type || "";

    // Connection errors
    if (message.includes("connection") || message.includes("connect")) {
      if (message.includes("timeout")) return ErrorTypes.CONNECTION_TIMEOUT;
      if (message.includes("lost") || message.includes("disconnect"))
        return ErrorTypes.CONNECTION_LOST;
      return ErrorTypes.CONNECTION_FAILED;
    }

    // Authentication errors
    if (
      message.includes("auth") ||
      message.includes("token") ||
      message.includes("unauthorized")
    ) {
      if (message.includes("expired")) return ErrorTypes.TOKEN_EXPIRED;
      return ErrorTypes.AUTH_FAILED;
    }

    // Audio errors
    if (
      contextType.includes("audio") ||
      message.includes("microphone") ||
      message.includes("track")
    ) {
      if (message.includes("permission") || message.includes("denied"))
        return ErrorTypes.MICROPHONE_ACCESS_DENIED;
      if (message.includes("track")) return ErrorTypes.AUDIO_TRACK_FAILED;
      return ErrorTypes.AUDIO_STREAM_ERROR;
    }

    // STT errors
    if (
      contextType.includes("stt") ||
      message.includes("deepgram") ||
      message.includes("transcription")
    ) {
      if (message.includes("quota") || message.includes("limit"))
        return ErrorTypes.STT_QUOTA_EXCEEDED;
      if (message.includes("format")) return ErrorTypes.STT_UNSUPPORTED_FORMAT;
      return ErrorTypes.STT_SERVICE_ERROR;
    }

    // Agent errors
    if (contextType.includes("agent") || message.includes("agent")) {
      if (message.includes("timeout")) return ErrorTypes.AGENT_TIMEOUT;
      if (message.includes("crash")) return ErrorTypes.AGENT_CRASHED;
      return ErrorTypes.AGENT_INIT_FAILED;
    }

    // Configuration errors
    if (
      message.includes("config") ||
      message.includes("environment") ||
      message.includes("missing")
    ) {
      if (message.includes("variable")) return ErrorTypes.ENV_VAR_MISSING;
      return ErrorTypes.CONFIG_MISSING;
    }

    // Network errors
    if (
      message.includes("network") ||
      message.includes("fetch") ||
      message.includes("request")
    ) {
      if (message.includes("rate") || message.includes("limit"))
        return ErrorTypes.RATE_LIMITED;
      return ErrorTypes.NETWORK_ERROR;
    }

    return ErrorTypes.UNKNOWN_ERROR;
  }

  /**
   * Determine error severity
   */
  determineSeverity(errorType, context) {
    const criticalErrors = [
      ErrorTypes.AGENT_CRASHED,
      ErrorTypes.CONFIG_MISSING,
      ErrorTypes.AUTH_FAILED,
    ];

    const highErrors = [
      ErrorTypes.CONNECTION_FAILED,
      ErrorTypes.AGENT_INIT_FAILED,
      ErrorTypes.STT_SERVICE_ERROR,
    ];

    const mediumErrors = [
      ErrorTypes.CONNECTION_TIMEOUT,
      ErrorTypes.AUDIO_TRACK_FAILED,
      ErrorTypes.TOKEN_EXPIRED,
    ];

    if (criticalErrors.includes(errorType)) return ErrorSeverity.CRITICAL;
    if (highErrors.includes(errorType)) return ErrorSeverity.HIGH;
    if (mediumErrors.includes(errorType)) return ErrorSeverity.MEDIUM;
    return ErrorSeverity.LOW;
  }

  /**
   * Attempt retry with exponential backoff
   */
  async attemptRetry(error, retryFunction, context) {
    const retryKey = this.generateRetryKey(error, context);
    const currentAttempts = this.retryAttempts.get(retryKey) || 0;

    if (currentAttempts >= this.options.maxRetries) {
      this.emit("retryExhausted", { error, attempts: currentAttempts });
      return { success: false, error, retriesExhausted: true };
    }

    const delay = this.calculateBackoffDelay(currentAttempts);
    this.retryAttempts.set(retryKey, currentAttempts + 1);

    this.emit("retryAttempt", {
      error,
      attempt: currentAttempts + 1,
      maxAttempts: this.options.maxRetries,
      delay,
    });

    // Wait before retry
    await this.sleep(delay);

    try {
      const result = await retryFunction();

      // Success - clear retry counter
      this.retryAttempts.delete(retryKey);
      this.emit("retrySuccess", { error, attempt: currentAttempts + 1 });

      return { success: true, result, retriesUsed: currentAttempts + 1 };
    } catch (retryError) {
      // Retry failed - handle the new error
      return await this.handleError(retryError, context, retryFunction);
    }
  }

  /**
   * Generate unique retry key
   */
  generateRetryKey(error, context) {
    return `${error.type}_${context.component || "unknown"}_${
      context.operation || "unknown"
    }`;
  }

  /**
   * Calculate exponential backoff delay
   */
  calculateBackoffDelay(attempt) {
    const delay = Math.min(
      this.options.baseDelay *
        Math.pow(this.options.backoffMultiplier, attempt),
      this.options.maxDelay
    );

    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay;
    return Math.floor(delay + jitter);
  }

  /**
   * Log error with appropriate level
   */
  logError(error) {
    if (!this.options.enableLogging) return;

    const logData = {
      type: error.type,
      message: error.message,
      severity: error.severity,
      context: error.context,
      timestamp: error.timestamp,
    };

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        console.error("🚨 CRITICAL ERROR:", logData);
        break;
      case ErrorSeverity.HIGH:
        console.error("❌ HIGH ERROR:", logData);
        break;
      case ErrorSeverity.MEDIUM:
        console.warn("⚠️ MEDIUM ERROR:", logData);
        break;
      case ErrorSeverity.LOW:
        console.log("ℹ️ LOW ERROR:", logData);
        break;
    }
  }

  /**
   * Add error to history
   */
  addToHistory(error) {
    this.errorHistory.unshift(error);

    // Limit history size
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    const stats = {
      total: this.errorHistory.length,
      bySeverity: {},
      byType: {},
      recent: this.errorHistory.slice(0, 10),
      activeRetries: this.retryAttempts.size,
    };

    // Count by severity
    Object.values(ErrorSeverity).forEach((severity) => {
      stats.bySeverity[severity] = this.errorHistory.filter(
        (e) => e.severity === severity
      ).length;
    });

    // Count by type
    Object.values(ErrorTypes).forEach((type) => {
      stats.byType[type] = this.errorHistory.filter(
        (e) => e.type === type
      ).length;
    });

    return stats;
  }

  /**
   * Clear error history and retry attempts
   */
  clearHistory() {
    this.errorHistory = [];
    this.retryAttempts.clear();
    this.emit("historyCleared");
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Create error handler for specific component
   */
  createComponentHandler(componentName) {
    return {
      handle: (error, context = {}) => {
        return this.handleError(error, {
          ...context,
          component: componentName,
        });
      },
      handleWithRetry: (error, retryFunction, context = {}) => {
        return this.handleError(
          error,
          { ...context, component: componentName },
          retryFunction
        );
      },
    };
  }
}

// Global error handler instance
export const globalErrorHandler = new ErrorHandler({
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 30000,
  enableLogging: true,
  enableUserNotifications: true,
});

// Convenience functions
export function createError(type, message, options = {}) {
  return new TranscriptionError(type, message, options);
}

export function handleError(error, context = {}, retryFunction = null) {
  return globalErrorHandler.handleError(error, context, retryFunction);
}

export function createComponentErrorHandler(componentName) {
  return globalErrorHandler.createComponentHandler(componentName);
}

export default {
  ErrorTypes,
  ErrorSeverity,
  TranscriptionError,
  ErrorHandler,
  globalErrorHandler,
  createError,
  handleError,
  createComponentErrorHandler,
};
