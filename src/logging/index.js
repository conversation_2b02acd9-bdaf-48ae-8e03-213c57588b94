/**
 * Production Logging System Integration
 * Combines all logging components into a unified system
 */

import {
  ProductionLogger,
  createLogger,
  requestLoggingMiddleware,
} from "./production-logger.js";
import { ErrorTracker, errorTracker, trackError } from "./error-tracker.js";
import {
  LogRotationManager,
  LogFileWatcher,
  rotationManager,
  fileWatcher,
} from "./log-rotation.js";

/**
 * Integrated Logging System
 * Provides a complete logging solution with structured logging, error tracking, and rotation
 */
export class IntegratedLoggingSystem {
  constructor(config = {}) {
    this.config = {
      // Service identification
      service:
        config.service || process.env.SERVICE_NAME || "livekit-transcription",
      version: config.version || process.env.SERVICE_VERSION || "1.0.0",
      environment: config.environment || process.env.NODE_ENV || "development",

      // Logging configuration
      logLevel: config.logLevel || process.env.LOG_LEVEL || "INFO",
      logDestination: config.logDestination || "both", // console, file, both
      logFormat: config.logFormat || "json",

      // File logging
      logDir: config.logDir || "./logs",
      enableRotation: config.enableRotation !== false,

      // Error tracking
      enableErrorTracking: config.enableErrorTracking !== false,
      errorThresholds: config.errorThresholds || {},
      alertChannels: config.alertChannels || ["console"],

      // Integration settings
      enableRequestLogging: config.enableRequestLogging !== false,
      enableMetrics: config.enableMetrics !== false,

      ...config,
    };

    this.components = {};
    this.isInitialized = false;

    this.init();
  }

  /**
   * Initialize the integrated logging system
   */
  async init() {
    try {
      // Initialize main logger
      this.components.logger = new ProductionLogger({
        component: "System",
        level: this.config.logLevel,
        destination: this.config.logDestination,
        format: this.config.logFormat,
        logDir: this.config.logDir,
        service: this.config.service,
        version: this.config.version,
        environment: this.config.environment,
      });

      // Initialize error tracker
      if (this.config.enableErrorTracking) {
        this.components.errorTracker = new ErrorTracker({
          ...this.config.errorThresholds,
          alertChannels: this.config.alertChannels,
          service: this.config.service,
        });

        // Connect error tracker to logger
        this.setupErrorTrackerIntegration();
      }

      // Initialize log rotation
      if (this.config.enableRotation) {
        this.components.rotationManager = new LogRotationManager({
          logDir: this.config.logDir,
        });

        this.components.fileWatcher = new LogFileWatcher(
          this.components.rotationManager
        );

        // Watch main log files
        this.setupLogFileWatching();
      }

      // Setup system-wide error handling
      this.setupGlobalErrorHandling();

      // Setup metrics collection
      if (this.config.enableMetrics) {
        this.setupMetricsCollection();
      }

      this.isInitialized = true;

      this.components.logger.info("Integrated logging system initialized", {
        config: this.config,
        components: Object.keys(this.components),
      });
    } catch (error) {
      console.error("Failed to initialize logging system:", error);
      throw error;
    }
  }

  /**
   * Setup error tracker integration with logger
   */
  setupErrorTrackerIntegration() {
    const logger = this.components.logger;
    const errorTracker = this.components.errorTracker;

    // Forward logger errors to error tracker
    logger.on("error", (errorData) => {
      errorTracker.trackError(errorData.error || errorData.message, {
        component: errorData.component,
        correlationId: errorData.correlationId,
        severity: this.mapLogLevelToSeverity(errorData.level),
      });
    });

    // Log error tracker alerts
    errorTracker.on("patternDetected", (alert) => {
      logger.warn("Error pattern detected", {
        alertId: alert.id,
        pattern: alert.pattern.type,
        severity: alert.severity,
        message: alert.message,
      });
    });

    errorTracker.on("criticalError", (alert) => {
      logger.fatal("Critical error alert", {
        alertId: alert.id,
        errorType: alert.errorEntry.error.type,
        component: alert.errorEntry.component,
        message: alert.message,
      });
    });
  }

  /**
   * Setup log file watching for rotation
   */
  setupLogFileWatching() {
    if (!this.components.fileWatcher) return;

    const logFiles = [
      `${this.config.logDir}/${this.config.service}-all.log`,
      `${this.config.logDir}/${this.config.service}-error.log`,
      `${this.config.logDir}/${this.config.service}-access.log`,
    ];

    logFiles.forEach((filePath) => {
      this.components.fileWatcher.watchFile(filePath);
    });
  }

  /**
   * Setup global error handling
   */
  setupGlobalErrorHandling() {
    const logger = this.components.logger;

    // Handle uncaught exceptions
    process.on("uncaughtException", (error) => {
      logger.fatal(
        "Uncaught exception",
        {
          error: error.message,
          stack: error.stack,
        },
        error
      );

      // Track the error
      if (this.components.errorTracker) {
        this.components.errorTracker.trackError(error, {
          component: "Process",
          severity: "critical",
          type: "uncaughtException",
        });
      }

      // Give time for logging to complete before exiting
      setTimeout(() => {
        process.exit(1);
      }, 1000);
    });

    // Handle unhandled promise rejections
    process.on("unhandledRejection", (reason, promise) => {
      logger.error("Unhandled promise rejection", {
        reason: reason?.message || reason,
        stack: reason?.stack,
        promise: promise.toString(),
      });

      // Track the error
      if (this.components.errorTracker) {
        this.components.errorTracker.trackError(reason, {
          component: "Process",
          severity: "high",
          type: "unhandledRejection",
        });
      }
    });

    // Handle process warnings
    process.on("warning", (warning) => {
      logger.warn("Process warning", {
        name: warning.name,
        message: warning.message,
        stack: warning.stack,
      });
    });
  }

  /**
   * Setup metrics collection
   */
  setupMetricsCollection() {
    const logger = this.components.logger;

    // Collect system metrics periodically
    setInterval(() => {
      const metrics = this.getSystemMetrics();
      logger.debug("System metrics", metrics);
    }, 60000); // Every minute

    // Log startup metrics
    logger.info("System startup metrics", this.getSystemMetrics());
  }

  /**
   * Get system metrics
   */
  getSystemMetrics() {
    const metrics = {
      timestamp: Date.now(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      pid: process.pid,
    };

    // Add component metrics if available
    if (this.components.errorTracker) {
      metrics.errorTracker = this.components.errorTracker.getStats();
    }

    if (this.components.rotationManager) {
      metrics.logRotation = this.components.rotationManager.getStats();
    }

    if (this.components.fileWatcher) {
      metrics.fileWatcher = this.components.fileWatcher.getStats();
    }

    return metrics;
  }

  /**
   * Map log level to error severity
   */
  mapLogLevelToSeverity(logLevel) {
    const mapping = {
      TRACE: "low",
      DEBUG: "low",
      INFO: "low",
      WARN: "medium",
      ERROR: "high",
      FATAL: "critical",
    };
    return mapping[logLevel] || "medium";
  }

  /**
   * Create a component-specific logger
   */
  createLogger(component, config = {}) {
    if (!this.isInitialized) {
      throw new Error("Logging system not initialized");
    }

    return this.components.logger.child(component, config);
  }

  /**
   * Track an error through the system
   */
  trackError(error, context = {}) {
    if (this.components.errorTracker) {
      return this.components.errorTracker.trackError(error, context);
    }

    // Fallback to just logging
    this.components.logger.error("Error tracked (no error tracker)", {
      error: error.message || error,
      context,
    });
  }

  /**
   * Get Express middleware for request logging
   */
  getRequestLoggingMiddleware() {
    if (!this.config.enableRequestLogging) {
      return (req, res, next) => next();
    }

    return requestLoggingMiddleware(this.components.logger);
  }

  /**
   * Force log rotation
   */
  async forceLogRotation(filePath) {
    if (this.components.rotationManager) {
      return await this.components.rotationManager.forceRotation(filePath);
    }
    throw new Error("Log rotation not enabled");
  }

  /**
   * Get comprehensive system status
   */
  getStatus() {
    const status = {
      initialized: this.isInitialized,
      config: this.config,
      components: {},
      metrics: this.getSystemMetrics(),
    };

    // Add component status
    Object.entries(this.components).forEach(([name, component]) => {
      if (typeof component.getStats === "function") {
        status.components[name] = component.getStats();
      } else {
        status.components[name] = { available: true };
      }
    });

    return status;
  }

  /**
   * Update system configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };

    // Update component configurations
    if (this.components.logger && newConfig.logLevel) {
      this.components.logger.setLevel(newConfig.logLevel);
    }

    if (this.components.rotationManager && newConfig.rotation) {
      this.components.rotationManager.updateConfig(newConfig.rotation);
    }

    this.components.logger.info("Logging system configuration updated", {
      newConfig,
    });
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    this.components.logger.info("Logging system shutting down");

    // Stop file watcher
    if (this.components.fileWatcher) {
      this.components.fileWatcher.stopWatching();
    }

    // Flush any remaining logs
    if (
      this.components.logger &&
      typeof this.components.logger.flushBuffer === "function"
    ) {
      this.components.logger.flushBuffer();
    }

    // Give time for final log writes
    await new Promise((resolve) => setTimeout(resolve, 1000));

    this.components.logger.info("Logging system shutdown complete");
  }
}

/**
 * Global integrated logging system instance
 */
export const loggingSystem = new IntegratedLoggingSystem();

/**
 * Convenience functions using the global system
 */
export function getLogger(component, config = {}) {
  return loggingSystem.createLogger(component, config);
}

export function trackSystemError(error, context = {}) {
  return loggingSystem.trackError(error, context);
}

export function getSystemStatus() {
  return loggingSystem.getStatus();
}

export function getRequestMiddleware() {
  return loggingSystem.getRequestLoggingMiddleware();
}

/**
 * Initialize logging system with custom configuration
 */
export async function initializeLogging(config = {}) {
  const system = new IntegratedLoggingSystem(config);
  await system.init();
  return system;
}

// Export all components for direct use if needed
export {
  ProductionLogger,
  ErrorTracker,
  LogRotationManager,
  LogFileWatcher,
  createLogger,
  errorTracker,
  trackError,
  rotationManager,
  fileWatcher,
  requestLoggingMiddleware,
};

export default {
  IntegratedLoggingSystem,
  loggingSystem,
  getLogger,
  trackSystemError,
  getSystemStatus,
  getRequestMiddleware,
  initializeLogging,

  // Components
  ProductionLogger,
  ErrorTracker,
  LogRotationManager,
  LogFileWatcher,
};
