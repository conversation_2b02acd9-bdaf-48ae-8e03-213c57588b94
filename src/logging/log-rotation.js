/**
 * Log Rotation and Retention System
 * Handles automatic log file rotation, compression, and cleanup
 */

import fs from "fs";
import path from "path";
import { createGzip } from "zlib";
import { pipeline } from "stream/promises";
import { createReadStream, createWriteStream } from "fs";
import { createLogger } from "./production-logger.js";

/**
 * Log Rotation Configuration
 */
const DEFAULT_ROTATION_CONFIG = {
  // Rotation triggers
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxAge: 24 * 60 * 60 * 1000, // 24 hours

  // Retention policy
  maxFiles: 10, // Keep 10 rotated files
  maxTotalSize: 100 * 1024 * 1024, // 100MB total

  // Compression
  compress: true,
  compressionLevel: 6,

  // Cleanup
  cleanupInterval: 60 * 60 * 1000, // 1 hour

  // File patterns
  datePattern: "YYYY-MM-DD-HH",
  extension: ".log",
  compressedExtension: ".gz",

  // Monitoring
  enableMetrics: true,
};

/**
 * Log Rotation Manager
 */
export class LogRotationManager {
  constructor(config = {}) {
    this.config = { ...DEFAULT_ROTATION_CONFIG, ...config };
    this.logger = createLogger("LogRotation");

    // State tracking
    this.rotationStats = {
      totalRotations: 0,
      totalCompressions: 0,
      totalCleanups: 0,
      bytesRotated: 0,
      bytesCompressed: 0,
      bytesDeleted: 0,
      lastRotation: null,
      lastCleanup: null,
    };

    // Active rotation locks to prevent concurrent operations
    this.rotationLocks = new Set();

    this.init();
  }

  /**
   * Initialize rotation manager
   */
  init() {
    // Setup cleanup interval
    this.setupCleanupInterval();

    // Setup graceful shutdown
    this.setupGracefulShutdown();

    this.logger.info("Log rotation manager initialized", {
      config: this.config,
      stats: this.rotationStats,
    });
  }

  /**
   * Check if file needs rotation
   */
  async needsRotation(filePath) {
    try {
      const stats = await fs.promises.stat(filePath);

      // Check file size
      if (stats.size >= this.config.maxFileSize) {
        return {
          reason: "size",
          size: stats.size,
          maxSize: this.config.maxFileSize,
        };
      }

      // Check file age
      const age = Date.now() - stats.mtime.getTime();
      if (age >= this.config.maxAge) {
        return { reason: "age", age, maxAge: this.config.maxAge };
      }

      return false;
    } catch (error) {
      if (error.code === "ENOENT") {
        return false; // File doesn't exist
      }
      throw error;
    }
  }

  /**
   * Rotate a log file
   */
  async rotateFile(filePath) {
    const lockKey = filePath;

    // Prevent concurrent rotation of the same file
    if (this.rotationLocks.has(lockKey)) {
      this.logger.debug("Rotation already in progress", { filePath });
      return false;
    }

    this.rotationLocks.add(lockKey);

    try {
      const rotationNeeded = await this.needsRotation(filePath);
      if (!rotationNeeded) {
        return false;
      }

      this.logger.info("Starting log rotation", {
        filePath,
        reason: rotationNeeded.reason,
        ...rotationNeeded,
      });

      const rotatedPath = await this.performRotation(filePath);

      // Compress if enabled
      if (this.config.compress) {
        await this.compressFile(rotatedPath);
      }

      // Update stats
      this.rotationStats.totalRotations++;
      this.rotationStats.lastRotation = Date.now();

      // Cleanup old files
      await this.cleanupOldFiles(
        path.dirname(filePath),
        path.basename(filePath)
      );

      this.logger.info("Log rotation completed", {
        filePath,
        rotatedPath,
        compressed: this.config.compress,
      });

      return true;
    } catch (error) {
      this.logger.error("Log rotation failed", {
        filePath,
        error: error.message,
      });
      throw error;
    } finally {
      this.rotationLocks.delete(lockKey);
    }
  }

  /**
   * Perform the actual file rotation
   */
  async performRotation(filePath) {
    const dir = path.dirname(filePath);
    const basename = path.basename(filePath, this.config.extension);
    const timestamp = this.generateTimestamp();

    // Generate rotated filename
    const rotatedFilename = `${basename}.${timestamp}${this.config.extension}`;
    const rotatedPath = path.join(dir, rotatedFilename);

    // Get file stats before rotation
    const stats = await fs.promises.stat(filePath);
    this.rotationStats.bytesRotated += stats.size;

    // Move current file to rotated name
    await fs.promises.rename(filePath, rotatedPath);

    // Create new empty file with same permissions
    await fs.promises.writeFile(filePath, "", { mode: stats.mode });

    return rotatedPath;
  }

  /**
   * Compress a log file
   */
  async compressFile(filePath) {
    const compressedPath = `${filePath}${this.config.compressedExtension}`;

    try {
      this.logger.debug("Compressing log file", { filePath, compressedPath });

      const gzip = createGzip({ level: this.config.compressionLevel });
      const source = createReadStream(filePath);
      const destination = createWriteStream(compressedPath);

      await pipeline(source, gzip, destination);

      // Get compression stats
      const originalStats = await fs.promises.stat(filePath);
      const compressedStats = await fs.promises.stat(compressedPath);

      const compressionRatio =
        (1 - compressedStats.size / originalStats.size) * 100;

      // Remove original file
      await fs.promises.unlink(filePath);

      // Update stats
      this.rotationStats.totalCompressions++;
      this.rotationStats.bytesCompressed += originalStats.size;

      this.logger.info("File compressed successfully", {
        filePath,
        compressedPath,
        originalSize: originalStats.size,
        compressedSize: compressedStats.size,
        compressionRatio: `${compressionRatio.toFixed(1)}%`,
      });

      return compressedPath;
    } catch (error) {
      this.logger.error("File compression failed", {
        filePath,
        error: error.message,
      });

      // Clean up partial compressed file
      try {
        await fs.promises.unlink(compressedPath);
      } catch (cleanupError) {
        // Ignore cleanup errors
      }

      throw error;
    }
  }

  /**
   * Clean up old log files based on retention policy
   */
  async cleanupOldFiles(logDir, baseFilename) {
    try {
      const files = await this.getLogFiles(logDir, baseFilename);

      // Sort by modification time (newest first)
      files.sort((a, b) => b.mtime - a.mtime);

      let totalSize = files.reduce((sum, file) => sum + file.size, 0);
      let filesToDelete = [];

      // Apply retention policies

      // 1. Keep only maxFiles
      if (files.length > this.config.maxFiles) {
        filesToDelete.push(...files.slice(this.config.maxFiles));
      }

      // 2. Enforce total size limit
      if (totalSize > this.config.maxTotalSize) {
        let currentSize = 0;
        for (let i = 0; i < files.length; i++) {
          currentSize += files[i].size;
          if (currentSize > this.config.maxTotalSize) {
            filesToDelete.push(...files.slice(i));
            break;
          }
        }
      }

      // Remove duplicates
      filesToDelete = [...new Set(filesToDelete)];

      // Delete files
      for (const file of filesToDelete) {
        try {
          await fs.promises.unlink(file.path);
          this.rotationStats.bytesDeleted += file.size;

          this.logger.debug("Old log file deleted", {
            filePath: file.path,
            size: file.size,
            age: Date.now() - file.mtime,
          });
        } catch (error) {
          this.logger.warn("Failed to delete old log file", {
            filePath: file.path,
            error: error.message,
          });
        }
      }

      if (filesToDelete.length > 0) {
        this.rotationStats.totalCleanups++;
        this.rotationStats.lastCleanup = Date.now();

        this.logger.info("Log cleanup completed", {
          filesDeleted: filesToDelete.length,
          bytesDeleted: filesToDelete.reduce((sum, f) => sum + f.size, 0),
          remainingFiles: files.length - filesToDelete.length,
        });
      }
    } catch (error) {
      this.logger.error("Log cleanup failed", { logDir, error: error.message });
    }
  }

  /**
   * Get all log files for a base filename
   */
  async getLogFiles(logDir, baseFilename) {
    try {
      const entries = await fs.promises.readdir(logDir, {
        withFileTypes: true,
      });
      const logFiles = [];

      for (const entry of entries) {
        if (!entry.isFile()) continue;

        const filename = entry.name;

        // Check if it's a rotated log file
        if (this.isRotatedLogFile(filename, baseFilename)) {
          const filePath = path.join(logDir, filename);
          const stats = await fs.promises.stat(filePath);

          logFiles.push({
            path: filePath,
            name: filename,
            size: stats.size,
            mtime: stats.mtime.getTime(),
            ctime: stats.ctime.getTime(),
          });
        }
      }

      return logFiles;
    } catch (error) {
      this.logger.error("Failed to get log files", {
        logDir,
        error: error.message,
      });
      return [];
    }
  }

  /**
   * Check if filename is a rotated log file
   */
  isRotatedLogFile(filename, baseFilename) {
    // Remove base filename and extensions
    const baseName = baseFilename.replace(this.config.extension, "");

    // Check for timestamp pattern
    const timestampPattern = /\.\d{4}-\d{2}-\d{2}-\d{2}/;

    // Check for compressed extension
    const hasCompressedExt = filename.endsWith(this.config.compressedExtension);
    const hasLogExt = filename.includes(this.config.extension);

    return (
      filename.startsWith(baseName) &&
      timestampPattern.test(filename) &&
      (hasLogExt || hasCompressedExt)
    );
  }

  /**
   * Generate timestamp for rotated files
   */
  generateTimestamp() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hour = String(now.getHours()).padStart(2, "0");

    return `${year}-${month}-${day}-${hour}`;
  }

  /**
   * Setup cleanup interval
   */
  setupCleanupInterval() {
    this.cleanupInterval = setInterval(async () => {
      try {
        await this.performScheduledCleanup();
      } catch (error) {
        this.logger.error("Scheduled cleanup failed", { error: error.message });
      }
    }, this.config.cleanupInterval);
  }

  /**
   * Perform scheduled cleanup of all log directories
   */
  async performScheduledCleanup() {
    this.logger.debug("Starting scheduled log cleanup");

    // This would be called with specific log directories
    // For now, we'll just update the last cleanup time
    this.rotationStats.lastCleanup = Date.now();
  }

  /**
   * Setup graceful shutdown
   */
  setupGracefulShutdown() {
    const shutdown = () => {
      this.logger.info("Log rotation manager shutting down");

      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }

      // Wait for any ongoing rotations to complete
      const waitForRotations = async () => {
        while (this.rotationLocks.size > 0) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      };

      waitForRotations().then(() => {
        this.logger.info("Log rotation manager shutdown complete");
      });
    };

    process.on("SIGTERM", shutdown);
    process.on("SIGINT", shutdown);
  }

  /**
   * Force rotation of a file (ignoring normal triggers)
   */
  async forceRotation(filePath) {
    this.logger.info("Forcing log rotation", { filePath });

    try {
      const rotatedPath = await this.performRotation(filePath);

      if (this.config.compress) {
        await this.compressFile(rotatedPath);
      }

      await this.cleanupOldFiles(
        path.dirname(filePath),
        path.basename(filePath)
      );

      this.rotationStats.totalRotations++;
      this.rotationStats.lastRotation = Date.now();

      return true;
    } catch (error) {
      this.logger.error("Forced rotation failed", {
        filePath,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Get rotation statistics
   */
  getStats() {
    return {
      ...this.rotationStats,
      activeRotations: this.rotationLocks.size,
      config: this.config,
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.logger.info("Log rotation configuration updated", { newConfig });
  }
}

/**
 * File Watcher for automatic rotation
 */
export class LogFileWatcher {
  constructor(rotationManager, config = {}) {
    this.rotationManager = rotationManager;
    this.config = {
      checkInterval: config.checkInterval || 60000, // 1 minute
      ...config,
    };

    this.logger = createLogger("LogFileWatcher");
    this.watchedFiles = new Map();
    this.checkInterval = null;

    this.init();
  }

  /**
   * Initialize file watcher
   */
  init() {
    this.startWatching();
    this.logger.info("Log file watcher initialized");
  }

  /**
   * Add file to watch list
   */
  watchFile(filePath) {
    if (!this.watchedFiles.has(filePath)) {
      this.watchedFiles.set(filePath, {
        path: filePath,
        lastCheck: Date.now(),
        lastSize: 0,
      });

      this.logger.debug("Added file to watch list", { filePath });
    }
  }

  /**
   * Remove file from watch list
   */
  unwatchFile(filePath) {
    if (this.watchedFiles.has(filePath)) {
      this.watchedFiles.delete(filePath);
      this.logger.debug("Removed file from watch list", { filePath });
    }
  }

  /**
   * Start watching files
   */
  startWatching() {
    this.checkInterval = setInterval(async () => {
      await this.checkFiles();
    }, this.config.checkInterval);
  }

  /**
   * Stop watching files
   */
  stopWatching() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Check all watched files for rotation needs
   */
  async checkFiles() {
    for (const [filePath, fileInfo] of this.watchedFiles) {
      try {
        const needsRotation = await this.rotationManager.needsRotation(
          filePath
        );
        if (needsRotation) {
          this.logger.info("Triggering automatic rotation", {
            filePath,
            reason: needsRotation.reason,
          });

          await this.rotationManager.rotateFile(filePath);
        }
      } catch (error) {
        this.logger.error("File check failed", {
          filePath,
          error: error.message,
        });
      }
    }
  }

  /**
   * Get watcher statistics
   */
  getStats() {
    return {
      watchedFiles: this.watchedFiles.size,
      files: Array.from(this.watchedFiles.keys()),
      config: this.config,
    };
  }
}

/**
 * Global rotation manager instance
 */
export const rotationManager = new LogRotationManager();

/**
 * Global file watcher instance
 */
export const fileWatcher = new LogFileWatcher(rotationManager);

export default {
  LogRotationManager,
  LogFileWatcher,
  rotationManager,
  fileWatcher,
};
