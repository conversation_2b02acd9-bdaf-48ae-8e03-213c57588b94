/**
 * Centralized Error Tracking and Alerting System
 * Provides error aggregation, pattern detection, and alerting capabilities
 */

import { EventEmitter } from "events";
import { createLogger } from "./production-logger.js";

/**
 * Error severity levels for alerting
 */
export const AlertSeverity = {
  LOW: "low",
  MEDIUM: "medium",
  HIGH: "high",
  CRITICAL: "critical",
};

/**
 * Alert channels
 */
export const AlertChannels = {
  EMAIL: "email",
  SLACK: "slack",
  WEBHOOK: "webhook",
  SMS: "sms",
  CONSOLE: "console",
};

/**
 * Error pattern types
 */
export const ErrorPatterns = {
  FREQUENCY: "frequency", // Too many errors in time window
  BURST: "burst", // Sudden spike in errors
  SEQUENCE: "sequence", // Specific error sequence
  THRESHOLD: "threshold", // Error rate threshold exceeded
  ANOMALY: "anomaly", // Unusual error pattern
};

/**
 * Centralized Error Tracker
 */
export class ErrorTracker extends EventEmitter {
  constructor(config = {}) {
    super();

    this.config = {
      // Time windows for analysis (in milliseconds)
      shortWindow: config.shortWindow || 60000, // 1 minute
      mediumWindow: config.mediumWindow || 300000, // 5 minutes
      longWindow: config.longWindow || 3600000, // 1 hour

      // Thresholds for alerting
      errorThresholds: {
        [AlertSeverity.LOW]: config.lowThreshold || 10,
        [AlertSeverity.MEDIUM]: config.mediumThreshold || 25,
        [AlertSeverity.HIGH]: config.highThreshold || 50,
        [AlertSeverity.CRITICAL]: config.criticalThreshold || 100,
      },

      // Rate thresholds (errors per minute)
      rateThresholds: {
        [AlertSeverity.LOW]: config.lowRate || 1,
        [AlertSeverity.MEDIUM]: config.mediumRate || 5,
        [AlertSeverity.HIGH]: config.highRate || 10,
        [AlertSeverity.CRITICAL]: config.criticalRate || 20,
      },

      // Alert configuration
      alertChannels: config.alertChannels || [AlertChannels.CONSOLE],
      alertCooldown: config.alertCooldown || 300000, // 5 minutes

      // Data retention
      maxErrorHistory: config.maxErrorHistory || 10000,
      cleanupInterval: config.cleanupInterval || 3600000, // 1 hour

      ...config,
    };

    this.logger = createLogger("ErrorTracker");

    // Error storage
    this.errorHistory = [];
    this.errorCounts = new Map();
    this.errorPatterns = new Map();
    this.alertHistory = new Map();

    // Pattern detection
    this.patternDetectors = new Map();

    // Initialize
    this.init();
  }

  /**
   * Initialize error tracker
   */
  init() {
    this.setupPatternDetectors();
    this.setupCleanupInterval();
    this.setupAlertChannels();

    this.logger.info("Error tracker initialized", {
      config: this.config,
      patterns: Array.from(this.patternDetectors.keys()),
    });
  }

  /**
   * Track an error
   */
  trackError(error, context = {}) {
    const errorEntry = {
      id: this.generateErrorId(),
      timestamp: Date.now(),
      error: this.normalizeError(error),
      context,
      component: context.component || "Unknown",
      correlationId: context.correlationId || "unknown",
      severity: this.determineSeverity(error, context),
    };

    // Add to history
    this.errorHistory.unshift(errorEntry);

    // Limit history size
    if (this.errorHistory.length > this.config.maxErrorHistory) {
      this.errorHistory = this.errorHistory.slice(
        0,
        this.config.maxErrorHistory
      );
    }

    // Update counts
    this.updateErrorCounts(errorEntry);

    // Detect patterns
    this.detectPatterns(errorEntry);

    // Check for alerts
    this.checkAlerts(errorEntry);

    // Emit event
    this.emit("errorTracked", errorEntry);

    this.logger.debug("Error tracked", {
      errorId: errorEntry.id,
      type: errorEntry.error.type,
      component: errorEntry.component,
      severity: errorEntry.severity,
    });

    return errorEntry.id;
  }

  /**
   * Normalize error object
   */
  normalizeError(error) {
    if (typeof error === "string") {
      return {
        type: "StringError",
        message: error,
        stack: null,
      };
    }

    if (error instanceof Error) {
      return {
        type: error.constructor.name,
        message: error.message,
        stack: error.stack,
        code: error.code,
      };
    }

    if (error && typeof error === "object") {
      return {
        type: error.type || error.name || "UnknownError",
        message: error.message || "Unknown error",
        stack: error.stack || null,
        code: error.code || null,
        ...error,
      };
    }

    return {
      type: "UnknownError",
      message: "Unknown error occurred",
      stack: null,
    };
  }

  /**
   * Determine error severity
   */
  determineSeverity(error, context) {
    // Check for explicit severity
    if (context.severity) {
      return context.severity;
    }

    if (error.severity) {
      return error.severity;
    }

    // Determine based on error type
    const errorType = error.type || error.name || error.constructor?.name;

    const criticalTypes = [
      "FATAL",
      "AGENT_CRASHED",
      "SYSTEM_FAILURE",
      "DATABASE_CONNECTION_FAILED",
    ];

    const highTypes = [
      "CONNECTION_FAILED",
      "AUTHENTICATION_FAILED",
      "SERVICE_UNAVAILABLE",
    ];

    const mediumTypes = ["TIMEOUT", "VALIDATION_ERROR", "RATE_LIMITED"];

    if (criticalTypes.some((type) => errorType.includes(type))) {
      return AlertSeverity.CRITICAL;
    }

    if (highTypes.some((type) => errorType.includes(type))) {
      return AlertSeverity.HIGH;
    }

    if (mediumTypes.some((type) => errorType.includes(type))) {
      return AlertSeverity.MEDIUM;
    }

    return AlertSeverity.LOW;
  }

  /**
   * Update error counts for different time windows
   */
  updateErrorCounts(errorEntry) {
    const now = errorEntry.timestamp;
    const errorKey = `${errorEntry.component}:${errorEntry.error.type}`;

    // Initialize counts if not exists
    if (!this.errorCounts.has(errorKey)) {
      this.errorCounts.set(errorKey, {
        total: 0,
        shortWindow: [],
        mediumWindow: [],
        longWindow: [],
      });
    }

    const counts = this.errorCounts.get(errorKey);
    counts.total++;

    // Add to time windows
    counts.shortWindow.push(now);
    counts.mediumWindow.push(now);
    counts.longWindow.push(now);

    // Clean old entries
    counts.shortWindow = counts.shortWindow.filter(
      (t) => now - t <= this.config.shortWindow
    );
    counts.mediumWindow = counts.mediumWindow.filter(
      (t) => now - t <= this.config.mediumWindow
    );
    counts.longWindow = counts.longWindow.filter(
      (t) => now - t <= this.config.longWindow
    );
  }

  /**
   * Setup pattern detectors
   */
  setupPatternDetectors() {
    // Frequency pattern detector
    this.patternDetectors.set(ErrorPatterns.FREQUENCY, (errorEntry) => {
      const errorKey = `${errorEntry.component}:${errorEntry.error.type}`;
      const counts = this.errorCounts.get(errorKey);

      if (
        counts &&
        counts.shortWindow.length >=
          this.config.errorThresholds[AlertSeverity.MEDIUM]
      ) {
        return {
          type: ErrorPatterns.FREQUENCY,
          severity: AlertSeverity.MEDIUM,
          message: `High frequency of ${errorEntry.error.type} errors in ${errorEntry.component}`,
          count: counts.shortWindow.length,
          timeWindow: "short",
        };
      }

      return null;
    });

    // Burst pattern detector
    this.patternDetectors.set(ErrorPatterns.BURST, (errorEntry) => {
      const now = errorEntry.timestamp;
      const recentErrors = this.errorHistory.filter(
        (e) =>
          now - e.timestamp <= 30000 && // Last 30 seconds
          e.component === errorEntry.component
      );

      if (recentErrors.length >= 10) {
        // 10 errors in 30 seconds
        return {
          type: ErrorPatterns.BURST,
          severity: AlertSeverity.HIGH,
          message: `Error burst detected in ${errorEntry.component}`,
          count: recentErrors.length,
          timeWindow: "30s",
        };
      }

      return null;
    });

    // Threshold pattern detector
    this.patternDetectors.set(ErrorPatterns.THRESHOLD, (errorEntry) => {
      const errorKey = `${errorEntry.component}:${errorEntry.error.type}`;
      const counts = this.errorCounts.get(errorKey);

      if (!counts) return null;

      // Check rate thresholds
      const rate = counts.shortWindow.length; // Errors per minute

      for (const [severity, threshold] of Object.entries(
        this.config.rateThresholds
      )) {
        if (rate >= threshold) {
          return {
            type: ErrorPatterns.THRESHOLD,
            severity,
            message: `Error rate threshold exceeded for ${errorEntry.error.type}`,
            rate,
            threshold,
            component: errorEntry.component,
          };
        }
      }

      return null;
    });
  }

  /**
   * Detect error patterns
   */
  detectPatterns(errorEntry) {
    for (const [patternType, detector] of this.patternDetectors) {
      try {
        const pattern = detector(errorEntry);
        if (pattern) {
          this.handlePattern(pattern, errorEntry);
        }
      } catch (error) {
        this.logger.error("Pattern detector error", {
          patternType,
          error: error.message,
        });
      }
    }
  }

  /**
   * Handle detected pattern
   */
  handlePattern(pattern, errorEntry) {
    const patternKey = `${pattern.type}:${errorEntry.component}:${errorEntry.error.type}`;

    // Check if we've already alerted for this pattern recently
    const lastAlert = this.alertHistory.get(patternKey);
    if (lastAlert && Date.now() - lastAlert < this.config.alertCooldown) {
      return;
    }

    // Record alert
    this.alertHistory.set(patternKey, Date.now());

    // Create alert
    const alert = {
      id: this.generateAlertId(),
      timestamp: Date.now(),
      pattern,
      errorEntry,
      severity: pattern.severity,
      message: pattern.message,
    };

    // Emit alert
    this.emit("patternDetected", alert);
    this.sendAlert(alert);

    this.logger.warn("Error pattern detected", {
      alertId: alert.id,
      pattern: pattern.type,
      severity: pattern.severity,
      component: errorEntry.component,
    });
  }

  /**
   * Check for immediate alerts
   */
  checkAlerts(errorEntry) {
    // Critical errors always trigger immediate alerts
    if (errorEntry.severity === AlertSeverity.CRITICAL) {
      const alert = {
        id: this.generateAlertId(),
        timestamp: Date.now(),
        type: "critical_error",
        errorEntry,
        severity: AlertSeverity.CRITICAL,
        message: `Critical error in ${errorEntry.component}: ${errorEntry.error.message}`,
      };

      this.emit("criticalError", alert);
      this.sendAlert(alert);
    }
  }

  /**
   * Setup alert channels
   */
  setupAlertChannels() {
    this.alertSenders = new Map();

    // Console alerter
    this.alertSenders.set(AlertChannels.CONSOLE, (alert) => {
      const color = this.getSeverityColor(alert.severity);
      const reset = "\x1b[0m";
      console.error(
        `${color}🚨 ALERT [${alert.severity.toUpperCase()}]: ${
          alert.message
        }${reset}`
      );
      console.error(`${color}   Alert ID: ${alert.id}${reset}`);
      console.error(
        `${color}   Timestamp: ${new Date(
          alert.timestamp
        ).toISOString()}${reset}`
      );
    });

    // Webhook alerter
    this.alertSenders.set(AlertChannels.WEBHOOK, async (alert) => {
      if (!this.config.webhookUrl) return;

      try {
        await fetch(this.config.webhookUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            alert,
            service: this.config.service || "livekit-transcription",
            environment: process.env.NODE_ENV || "development",
          }),
        });
      } catch (error) {
        this.logger.error("Failed to send webhook alert", {
          error: error.message,
        });
      }
    });

    // Email alerter (placeholder)
    this.alertSenders.set(AlertChannels.EMAIL, async (alert) => {
      // Implementation would depend on email service (SendGrid, SES, etc.)
      this.logger.info("Email alert would be sent", { alertId: alert.id });
    });

    // Slack alerter (placeholder)
    this.alertSenders.set(AlertChannels.SLACK, async (alert) => {
      // Implementation would depend on Slack webhook configuration
      this.logger.info("Slack alert would be sent", { alertId: alert.id });
    });
  }

  /**
   * Send alert through configured channels
   */
  async sendAlert(alert) {
    for (const channel of this.config.alertChannels) {
      const sender = this.alertSenders.get(channel);
      if (sender) {
        try {
          await sender(alert);
        } catch (error) {
          this.logger.error("Failed to send alert", {
            channel,
            alertId: alert.id,
            error: error.message,
          });
        }
      }
    }
  }

  /**
   * Get color for severity level
   */
  getSeverityColor(severity) {
    const colors = {
      [AlertSeverity.LOW]: "\x1b[36m", // Cyan
      [AlertSeverity.MEDIUM]: "\x1b[33m", // Yellow
      [AlertSeverity.HIGH]: "\x1b[31m", // Red
      [AlertSeverity.CRITICAL]: "\x1b[35m", // Magenta
    };
    return colors[severity] || "\x1b[37m";
  }

  /**
   * Setup cleanup interval
   */
  setupCleanupInterval() {
    setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * Cleanup old data
   */
  cleanup() {
    const now = Date.now();
    const cutoff = now - this.config.longWindow;

    // Clean error history
    this.errorHistory = this.errorHistory.filter((e) => e.timestamp > cutoff);

    // Clean error counts
    for (const [key, counts] of this.errorCounts) {
      counts.shortWindow = counts.shortWindow.filter(
        (t) => now - t <= this.config.shortWindow
      );
      counts.mediumWindow = counts.mediumWindow.filter(
        (t) => now - t <= this.config.mediumWindow
      );
      counts.longWindow = counts.longWindow.filter(
        (t) => now - t <= this.config.longWindow
      );

      // Remove empty entries
      if (counts.longWindow.length === 0) {
        this.errorCounts.delete(key);
      }
    }

    // Clean alert history
    for (const [key, timestamp] of this.alertHistory) {
      if (now - timestamp > this.config.alertCooldown * 2) {
        this.alertHistory.delete(key);
      }
    }

    this.logger.debug("Error tracker cleanup completed", {
      errorHistorySize: this.errorHistory.length,
      errorCountsSize: this.errorCounts.size,
      alertHistorySize: this.alertHistory.size,
    });
  }

  /**
   * Generate unique error ID
   */
  generateErrorId() {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique alert ID
   */
  generateAlertId() {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get error statistics
   */
  getStats() {
    const now = Date.now();

    // Calculate error rates
    const shortWindowErrors = this.errorHistory.filter(
      (e) => now - e.timestamp <= this.config.shortWindow
    );
    const mediumWindowErrors = this.errorHistory.filter(
      (e) => now - e.timestamp <= this.config.mediumWindow
    );
    const longWindowErrors = this.errorHistory.filter(
      (e) => now - e.timestamp <= this.config.longWindow
    );

    // Group by severity
    const bySeverity = {};
    Object.values(AlertSeverity).forEach((severity) => {
      bySeverity[severity] = this.errorHistory.filter(
        (e) => e.severity === severity
      ).length;
    });

    // Group by component
    const byComponent = {};
    this.errorHistory.forEach((e) => {
      byComponent[e.component] = (byComponent[e.component] || 0) + 1;
    });

    return {
      totalErrors: this.errorHistory.length,
      errorRates: {
        shortWindow: shortWindowErrors.length,
        mediumWindow: mediumWindowErrors.length,
        longWindow: longWindowErrors.length,
      },
      bySeverity,
      byComponent,
      activePatterns: this.errorPatterns.size,
      alertsSent: this.alertHistory.size,
      config: this.config,
    };
  }

  /**
   * Get recent errors
   */
  getRecentErrors(limit = 50) {
    return this.errorHistory.slice(0, limit);
  }

  /**
   * Search errors by criteria
   */
  searchErrors(criteria = {}) {
    return this.errorHistory.filter((error) => {
      if (criteria.component && error.component !== criteria.component) {
        return false;
      }

      if (criteria.severity && error.severity !== criteria.severity) {
        return false;
      }

      if (criteria.type && error.error.type !== criteria.type) {
        return false;
      }

      if (criteria.since && error.timestamp < criteria.since) {
        return false;
      }

      if (criteria.until && error.timestamp > criteria.until) {
        return false;
      }

      return true;
    });
  }
}

/**
 * Global error tracker instance
 */
export const errorTracker = new ErrorTracker();

/**
 * Convenience function to track errors
 */
export function trackError(error, context = {}) {
  return errorTracker.trackError(error, context);
}

export default {
  ErrorTracker,
  AlertSeverity,
  AlertChannels,
  ErrorPatterns,
  errorTracker,
  trackError,
};
