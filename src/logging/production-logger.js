/**
 * Production-Ready Logging System
 * Provides structured JSON logging, correlation IDs, log rotation, and centralized error tracking
 */

import fs from "fs";
import path from "path";
import os from "os";
import { fileURLToPath } from "url";
import { createWriteStream } from "fs";
import { EventEmitter } from "events";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Log levels with numeric priorities
export const LogLevels = {
  TRACE: { name: "TRACE", priority: 0, color: "\x1b[90m" },
  DEBUG: { name: "DEBUG", priority: 1, color: "\x1b[36m" },
  INFO: { name: "INFO", priority: 2, color: "\x1b[32m" },
  WARN: { name: "WARN", priority: 3, color: "\x1b[33m" },
  ERROR: { name: "ERROR", priority: 4, color: "\x1b[31m" },
  FATAL: { name: "FATAL", priority: 5, color: "\x1b[35m" },
};

// Log destinations
export const LogDestinations = {
  CONSOLE: "console",
  FILE: "file",
  BOTH: "both",
  REMOTE: "remote",
};

// Production Logger Configuration
const DEFAULT_CONFIG = {
  level: process.env.LOG_LEVEL || "INFO",
  destination: process.env.LOG_DESTINATION || LogDestinations.BOTH,
  format: process.env.LOG_FORMAT || "json", // 'json' or 'text'

  // File logging configuration
  logDir: process.env.LOG_DIR || path.join(process.cwd(), "logs"),
  maxFileSize: parseInt(process.env.LOG_MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
  maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5,

  // Console configuration
  colorize: process.env.LOG_COLORIZE !== "false",

  // Remote logging configuration
  remoteEndpoint: process.env.LOG_REMOTE_ENDPOINT,
  remoteApiKey: process.env.LOG_REMOTE_API_KEY,

  // Application metadata
  service: process.env.SERVICE_NAME || "livekit-transcription",
  version: process.env.SERVICE_VERSION || "1.0.0",
  environment: process.env.NODE_ENV || "development",

  // Performance settings
  bufferSize: parseInt(process.env.LOG_BUFFER_SIZE) || 100,
  flushInterval: parseInt(process.env.LOG_FLUSH_INTERVAL) || 5000, // 5 seconds
};

/**
 * Production Logger Class
 * Handles structured logging with correlation IDs, log rotation, and multiple destinations
 */
export class ProductionLogger extends EventEmitter {
  constructor(config = {}) {
    super();

    this.config = { ...DEFAULT_CONFIG, ...config };
    this.correlationId = this.generateCorrelationId();
    this.component = config.component || "Unknown";

    // Internal state
    this.logBuffer = [];
    this.fileStreams = new Map();
    this.isShuttingDown = false;

    // Initialize logging system
    this.init();
  }

  /**
   * Initialize the logging system
   */
  init() {
    // Create log directory if it doesn't exist
    if (
      this.config.destination === LogDestinations.FILE ||
      this.config.destination === LogDestinations.BOTH
    ) {
      this.ensureLogDirectory();
      this.setupFileStreams();
    }

    // Setup periodic buffer flush
    this.setupBufferFlush();

    // Setup graceful shutdown
    this.setupGracefulShutdown();

    // Setup error tracking
    this.setupErrorTracking();

    this.info("Production logger initialized", {
      config: {
        level: this.config.level,
        destination: this.config.destination,
        format: this.config.format,
        service: this.config.service,
        environment: this.config.environment,
      },
    });
  }

  /**
   * Generate unique correlation ID
   */
  generateCorrelationId() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 9);
    return `${timestamp}-${random}`;
  }

  /**
   * Create child logger with same correlation ID
   */
  child(component, metadata = {}) {
    const childLogger = new ProductionLogger({
      ...this.config,
      component,
      ...metadata,
    });
    childLogger.correlationId = this.correlationId;
    return childLogger;
  }

  /**
   * Log methods for different levels
   */
  trace(message, metadata = {}) {
    this.log(LogLevels.TRACE, message, metadata);
  }

  debug(message, metadata = {}) {
    this.log(LogLevels.DEBUG, message, metadata);
  }

  info(message, metadata = {}) {
    this.log(LogLevels.INFO, message, metadata);
  }

  warn(message, metadata = {}) {
    this.log(LogLevels.WARN, message, metadata);
  }

  error(message, metadata = {}, error = null) {
    const errorMetadata = { ...metadata };

    if (error) {
      errorMetadata.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: error.code,
      };
    }

    this.log(LogLevels.ERROR, message, errorMetadata);

    // Emit error event for centralized error tracking
    this.emit("error", {
      message,
      metadata: errorMetadata,
      error,
      correlationId: this.correlationId,
      component: this.component,
      timestamp: new Date().toISOString(),
    });
  }

  fatal(message, metadata = {}, error = null) {
    this.error(message, metadata, error);
    this.log(LogLevels.FATAL, message, metadata);

    // Emit fatal event
    this.emit("fatal", {
      message,
      metadata,
      error,
      correlationId: this.correlationId,
      component: this.component,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Core logging method
   */
  log(level, message, metadata = {}) {
    // Check if log level should be processed
    if (!this.shouldLog(level)) {
      return;
    }

    const logEntry = this.createLogEntry(level, message, metadata);

    // Add to buffer for batch processing
    this.logBuffer.push(logEntry);

    // Immediate flush for high priority logs
    if (level.priority >= LogLevels.ERROR.priority) {
      this.flushBuffer();
    }

    // Emit log event for real-time processing
    this.emit("log", logEntry);
  }

  /**
   * Create structured log entry
   */
  createLogEntry(level, message, metadata = {}) {
    const timestamp = new Date().toISOString();

    const baseEntry = {
      timestamp,
      level: level.name,
      message,
      correlationId: this.correlationId,
      component: this.component,
      service: this.config.service,
      version: this.config.version,
      environment: this.config.environment,
      pid: process.pid,
      hostname: process.env.HOSTNAME || os.hostname(),
      ...metadata,
    };

    // Add performance metrics if available
    if (process.memoryUsage) {
      const memory = process.memoryUsage();
      baseEntry.performance = {
        memoryUsage: {
          rss: memory.rss,
          heapUsed: memory.heapUsed,
          heapTotal: memory.heapTotal,
          external: memory.external,
        },
        uptime: process.uptime(),
      };
    }

    return baseEntry;
  }

  /**
   * Check if log level should be processed
   */
  shouldLog(level) {
    const configLevel = LogLevels[this.config.level.toUpperCase()];
    return level.priority >= configLevel.priority;
  }

  /**
   * Setup file streams for different log levels
   */
  setupFileStreams() {
    const logTypes = ["all", "error", "access"];

    logTypes.forEach((type) => {
      const filename = `${this.config.service}-${type}.log`;
      const filepath = path.join(this.config.logDir, filename);

      const stream = createWriteStream(filepath, {
        flags: "a",
        encoding: "utf8",
      });

      stream.on("error", (error) => {
        console.error(`Log file stream error for ${type}:`, error);
      });

      this.fileStreams.set(type, stream);
    });
  }

  /**
   * Ensure log directory exists
   */
  ensureLogDirectory() {
    if (!fs.existsSync(this.config.logDir)) {
      fs.mkdirSync(this.config.logDir, { recursive: true });
    }
  }

  /**
   * Setup periodic buffer flush
   */
  setupBufferFlush() {
    this.flushInterval = setInterval(() => {
      if (this.logBuffer.length > 0) {
        this.flushBuffer();
      }
    }, this.config.flushInterval);
  }

  /**
   * Flush log buffer to destinations
   */
  flushBuffer() {
    if (this.logBuffer.length === 0 || this.isShuttingDown) {
      return;
    }

    const entries = [...this.logBuffer];
    this.logBuffer = [];

    entries.forEach((entry) => {
      this.writeToDestinations(entry);
    });
  }

  /**
   * Write log entry to configured destinations
   */
  writeToDestinations(entry) {
    const { destination } = this.config;

    // Console output
    if (
      destination === LogDestinations.CONSOLE ||
      destination === LogDestinations.BOTH
    ) {
      this.writeToConsole(entry);
    }

    // File output
    if (
      destination === LogDestinations.FILE ||
      destination === LogDestinations.BOTH
    ) {
      this.writeToFile(entry);
    }

    // Remote output
    if (destination === LogDestinations.REMOTE && this.config.remoteEndpoint) {
      this.writeToRemote(entry);
    }
  }

  /**
   * Write to console with formatting
   */
  writeToConsole(entry) {
    const level = LogLevels[entry.level];
    const color = this.config.colorize ? level.color : "";
    const reset = this.config.colorize ? "\x1b[0m" : "";

    if (this.config.format === "json") {
      console.log(`${color}${JSON.stringify(entry)}${reset}`);
    } else {
      const formatted = `${color}[${entry.timestamp}] ${entry.level} [${entry.component}] ${entry.correlationId}: ${entry.message}${reset}`;
      console.log(formatted);

      // Log metadata if present
      if (Object.keys(entry).length > 8) {
        // More than base fields
        const metadata = { ...entry };
        delete metadata.timestamp;
        delete metadata.level;
        delete metadata.message;
        delete metadata.correlationId;
        delete metadata.component;
        delete metadata.service;
        delete metadata.version;
        delete metadata.environment;

        console.log(
          `${color}  Metadata: ${JSON.stringify(metadata, null, 2)}${reset}`
        );
      }
    }
  }

  /**
   * Write to file with rotation
   */
  writeToFile(entry) {
    const allStream = this.fileStreams.get("all");
    const errorStream = this.fileStreams.get("error");

    if (allStream) {
      allStream.write(JSON.stringify(entry) + "\n");
    }

    // Write errors to separate error log
    if (entry.level === "ERROR" || entry.level === "FATAL") {
      if (errorStream) {
        errorStream.write(JSON.stringify(entry) + "\n");
      }
    }

    // Check for log rotation
    this.checkLogRotation();
  }

  /**
   * Write to remote logging service
   */
  async writeToRemote(entry) {
    if (!this.config.remoteEndpoint) {
      return;
    }

    try {
      const response = await fetch(this.config.remoteEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${this.config.remoteApiKey}`,
        },
        body: JSON.stringify(entry),
      });

      if (!response.ok) {
        console.error(
          "Failed to send log to remote endpoint:",
          response.statusText
        );
      }
    } catch (error) {
      console.error("Error sending log to remote endpoint:", error.message);
    }
  }

  /**
   * Check and perform log rotation
   */
  checkLogRotation() {
    this.fileStreams.forEach((stream, type) => {
      const filename = `${this.config.service}-${type}.log`;
      const filepath = path.join(this.config.logDir, filename);

      try {
        const stats = fs.statSync(filepath);
        if (stats.size > this.config.maxFileSize) {
          this.rotateLogFile(type, filepath);
        }
      } catch (error) {
        // File might not exist yet, ignore
      }
    });
  }

  /**
   * Rotate log file
   */
  rotateLogFile(type, filepath) {
    const stream = this.fileStreams.get(type);
    if (stream) {
      stream.end();
    }

    // Rotate existing files
    for (let i = this.config.maxFiles - 1; i > 0; i--) {
      const oldFile = `${filepath}.${i}`;
      const newFile = `${filepath}.${i + 1}`;

      if (fs.existsSync(oldFile)) {
        if (i === this.config.maxFiles - 1) {
          fs.unlinkSync(oldFile); // Delete oldest file
        } else {
          fs.renameSync(oldFile, newFile);
        }
      }
    }

    // Move current file to .1
    if (fs.existsSync(filepath)) {
      fs.renameSync(filepath, `${filepath}.1`);
    }

    // Create new stream
    const newStream = createWriteStream(filepath, {
      flags: "a",
      encoding: "utf8",
    });

    newStream.on("error", (error) => {
      console.error(`Log file stream error for ${type}:`, error);
    });

    this.fileStreams.set(type, newStream);

    this.info("Log file rotated", { type, filepath });
  }

  /**
   * Setup error tracking and alerting
   */
  setupErrorTracking() {
    // Track error patterns
    this.errorCounts = new Map();
    this.errorPatterns = new Map();

    this.on("error", (errorData) => {
      const errorKey = `${errorData.component}:${errorData.message}`;
      const count = this.errorCounts.get(errorKey) || 0;
      this.errorCounts.set(errorKey, count + 1);

      // Alert on error threshold
      if (count + 1 >= 5) {
        // 5 errors of same type
        this.emit("errorThreshold", {
          errorKey,
          count: count + 1,
          errorData,
        });
      }
    });

    this.on("fatal", (fatalData) => {
      // Immediate alert for fatal errors
      this.emit("fatalAlert", fatalData);
    });
  }

  /**
   * Setup graceful shutdown
   */
  setupGracefulShutdown() {
    const shutdown = () => {
      this.isShuttingDown = true;
      this.info("Logger shutting down gracefully");

      // Flush remaining logs
      this.flushBuffer();

      // Close file streams
      this.fileStreams.forEach((stream) => {
        stream.end();
      });

      // Clear intervals
      if (this.flushInterval) {
        clearInterval(this.flushInterval);
      }
    };

    process.on("SIGTERM", shutdown);
    process.on("SIGINT", shutdown);
    process.on("beforeExit", shutdown);
  }

  /**
   * Get logging statistics
   */
  getStats() {
    return {
      bufferSize: this.logBuffer.length,
      errorCounts: Object.fromEntries(this.errorCounts),
      config: this.config,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
    };
  }

  /**
   * Update log level dynamically
   */
  setLevel(level) {
    if (LogLevels[level.toUpperCase()]) {
      this.config.level = level.toUpperCase();
      this.info("Log level updated", { newLevel: level });
    } else {
      this.warn("Invalid log level", { attemptedLevel: level });
    }
  }

  /**
   * Create access log entry (for HTTP requests, etc.)
   */
  access(method, url, statusCode, responseTime, metadata = {}) {
    const accessEntry = {
      type: "access",
      method,
      url,
      statusCode,
      responseTime,
      ...metadata,
    };

    this.info("Access log", accessEntry);

    // Write to access log file if available
    const accessStream = this.fileStreams.get("access");
    if (accessStream) {
      accessStream.write(
        JSON.stringify({
          ...this.createLogEntry(LogLevels.INFO, "Access", accessEntry),
          type: "access",
        }) + "\n"
      );
    }
  }

  /**
   * Create audit log entry (for security events, etc.)
   */
  audit(action, resource, user, result, metadata = {}) {
    const auditEntry = {
      type: "audit",
      action,
      resource,
      user,
      result,
      ...metadata,
    };

    this.info("Audit log", auditEntry);

    // Emit audit event for external processing
    this.emit("audit", {
      ...auditEntry,
      correlationId: this.correlationId,
      timestamp: new Date().toISOString(),
    });
  }
}

/**
 * Global logger instance
 */
export const logger = new ProductionLogger({
  component: "Global",
});

/**
 * Create component-specific logger
 */
export function createLogger(component, config = {}) {
  return new ProductionLogger({
    component,
    ...config,
  });
}

/**
 * Express middleware for request logging
 */
export function requestLoggingMiddleware(logger) {
  return (req, res, next) => {
    const start = Date.now();
    const correlationId =
      req.headers["x-correlation-id"] || logger.generateCorrelationId();

    // Add correlation ID to request
    req.correlationId = correlationId;
    res.setHeader("X-Correlation-ID", correlationId);

    // Create request-specific logger
    req.logger = logger.child("HTTP", { correlationId });

    // Log request
    req.logger.info("HTTP Request", {
      method: req.method,
      url: req.url,
      userAgent: req.get("User-Agent"),
      ip: req.ip,
      headers: req.headers,
    });

    // Log response
    res.on("finish", () => {
      const responseTime = Date.now() - start;

      req.logger.access(req.method, req.url, res.statusCode, responseTime, {
        ip: req.ip,
        userAgent: req.get("User-Agent"),
        contentLength: res.get("Content-Length"),
      });
    });

    next();
  };
}

export default {
  ProductionLogger,
  LogLevels,
  LogDestinations,
  logger,
  createLogger,
  requestLoggingMiddleware,
};
