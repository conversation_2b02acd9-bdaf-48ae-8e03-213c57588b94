# 🚀 启动测试指南

## ✅ 修复完成

已经修复了代理启动问题。现在可以正确使用以下命令：

### 启动命令

```bash
# 生产模式启动代理
npm run agent

# 开发模式启动代理（更多日志）
npm run agent:dev

# 启动Web服务器
npm start
```

### 验证步骤

1. **检查环境配置**

   ```bash
   npm run validate
   ```

2. **启动 Web 服务器**

   ```bash
   npm start
   ```

   应该看到：`Server running on http://localhost:3000`

3. **启动转录代理**（新终端窗口）

   ```bash
   npm run agent
   ```

   应该看到 LiveKit Agents 启动信息

4. **访问应用**
   打开浏览器访问：`http://localhost:3000`

### 常用命令

```bash
# 查看代理帮助
node src/enhanced-agent-v2.js --help

# 查看可用命令
node src/enhanced-agent-v2.js start --help
node src/enhanced-agent-v2.js dev --help
node src/enhanced-agent-v2.js connect --help

# 健康检查
npm run health
npm run test:health
```

### 故障排除

如果遇到问题：

1. **检查环境变量**

   ```bash
   npm run validate
   ```

2. **查看详细日志**

   ```bash
   npm run agent:dev
   ```

3. **检查端口占用**
   ```bash
   lsof -i :3000  # 检查3000端口
   ```

## 🎯 项目现在可以正常启动了！

使用 `npm run agent` 启动转录代理，使用 `npm start` 启动 Web 服务器。
