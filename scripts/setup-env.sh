#!/bin/bash

# LiveKit Agents + Deepgram Environment Setup Script
# This script helps you set up the environment configuration

set -e

echo "🚀 LiveKit Agents + Deepgram Environment Setup"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to prompt for input with default value
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " input
        if [ -z "$input" ]; then
            input="$default"
        fi
    else
        read -p "$prompt: " input
        while [ -z "$input" ]; do
            echo -e "${RED}This field is required!${NC}"
            read -p "$prompt: " input
        done
    fi
    
    eval "$var_name='$input'"
}

# Function to validate URL format
validate_url() {
    local url="$1"
    if [[ $url =~ ^wss?:// ]]; then
        return 0
    else
        return 1
    fi
}

# Function to test API connectivity
test_connectivity() {
    echo -e "\n${BLUE}🔍 Testing connectivity...${NC}"
    
    # Test LiveKit connectivity (basic check)
    if command -v curl &> /dev/null; then
        echo "Testing LiveKit URL accessibility..."
        # Convert wss:// to https:// for basic connectivity test
        http_url=$(echo "$LIVEKIT_URL" | sed 's/wss:/https:/')
        if curl -s --connect-timeout 10 "$http_url" &> /dev/null; then
            echo -e "${GREEN}✅ LiveKit URL is accessible${NC}"
        else
            echo -e "${YELLOW}⚠️ LiveKit URL test inconclusive (this may be normal)${NC}"
        fi
    fi
    
    # Test Deepgram API
    if [ -n "$DEEPGRAM_API_KEY" ]; then
        echo "Testing Deepgram API key..."
        if curl -s -H "Authorization: Token $DEEPGRAM_API_KEY" \
           "https://api.deepgram.com/v1/projects" &> /dev/null; then
            echo -e "${GREEN}✅ Deepgram API key is valid${NC}"
        else
            echo -e "${YELLOW}⚠️ Deepgram API key test failed${NC}"
        fi
    fi
}

# Check if .env already exists
if [ -f ".env" ]; then
    echo -e "${YELLOW}⚠️ .env file already exists!${NC}"
    read -p "Do you want to overwrite it? (y/N): " overwrite
    if [[ ! $overwrite =~ ^[Yy]$ ]]; then
        echo "Setup cancelled."
        exit 0
    fi
fi

echo -e "\n${BLUE}📝 Please provide the following configuration:${NC}"

# LiveKit Configuration
echo -e "\n${BLUE}LiveKit Configuration:${NC}"
echo "Get these from your LiveKit Cloud dashboard or self-hosted server"

prompt_with_default "LiveKit Server URL (wss://...)" "" "LIVEKIT_URL"
while ! validate_url "$LIVEKIT_URL"; do
    echo -e "${RED}Invalid URL format. Please use wss:// or ws://${NC}"
    prompt_with_default "LiveKit Server URL (wss://...)" "" "LIVEKIT_URL"
done

prompt_with_default "LiveKit API Key" "" "LIVEKIT_API_KEY"
prompt_with_default "LiveKit API Secret" "" "LIVEKIT_API_SECRET"

# Deepgram Configuration
echo -e "\n${BLUE}Deepgram Configuration:${NC}"
echo "Get your API key from: https://console.deepgram.com/"

prompt_with_default "Deepgram API Key" "" "DEEPGRAM_API_KEY"

# OpenAI Configuration (Optional)
echo -e "\n${BLUE}OpenAI Configuration (Optional):${NC}"
echo "Required for MultimodalAgent with LLM features. Leave empty to use direct STT approach."

prompt_with_default "OpenAI API Key (optional)" "" "OPENAI_API_KEY"

# Server Configuration
echo -e "\n${BLUE}Server Configuration:${NC}"

prompt_with_default "Server Port" "3000" "PORT"
prompt_with_default "Environment" "development" "NODE_ENV"

# Advanced Configuration
echo -e "\n${BLUE}Advanced Configuration (optional):${NC}"

prompt_with_default "Default Room Name" "transcription-room" "DEFAULT_ROOM_NAME"
prompt_with_default "Transcription Language (en/zh/multi)" "multi" "TRANSCRIPTION_LANGUAGE"
prompt_with_default "Enable Speaker Diarization (true/false)" "true" "ENABLE_DIARIZATION"

# Create .env file
echo -e "\n${BLUE}📄 Creating .env file...${NC}"

cat > .env << EOF
# LiveKit Agents + Deepgram Transcription System
# Generated by setup script on $(date)

# =============================================================================
# LIVEKIT CONFIGURATION
# =============================================================================
LIVEKIT_URL=$LIVEKIT_URL
LIVEKIT_API_KEY=$LIVEKIT_API_KEY
LIVEKIT_API_SECRET=$LIVEKIT_API_SECRET

# =============================================================================
# DEEPGRAM CONFIGURATION
# =============================================================================
DEEPGRAM_API_KEY=$DEEPGRAM_API_KEY

# =============================================================================
# OPENAI CONFIGURATION
# =============================================================================
OPENAI_API_KEY=$OPENAI_API_KEY

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
PORT=$PORT
NODE_ENV=$NODE_ENV

# =============================================================================
# TRANSCRIPTION CONFIGURATION
# =============================================================================
DEFAULT_ROOM_NAME=$DEFAULT_ROOM_NAME
TRANSCRIPTION_LANGUAGE=$TRANSCRIPTION_LANGUAGE
DEEPGRAM_MODEL=nova-2
ENABLE_DIARIZATION=$ENABLE_DIARIZATION
ENABLE_SMART_FORMAT=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
JSON_LOGGING=false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
JWT_TTL=10m
ENABLE_CORS=true
CORS_ORIGINS=http://localhost:$PORT

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
ENABLE_HEALTH_CHECKS=true
ENABLE_METRICS=true
METRICS_PORT=9090
EOF

echo -e "${GREEN}✅ .env file created successfully!${NC}"

# Test connectivity if requested
read -p "Do you want to test the configuration? (Y/n): " test_config
if [[ ! $test_config =~ ^[Nn]$ ]]; then
    test_connectivity
fi

# Installation instructions
echo -e "\n${BLUE}🎯 Next Steps:${NC}"
echo "1. Install dependencies: npm install"
echo "2. Start the web server: npm start"
echo "3. Start the agent: node src/enhanced-agent-v2.js"
echo "4. Open your browser: http://localhost:$PORT"

echo -e "\n${GREEN}🎉 Setup complete! Your LiveKit Agents + Deepgram system is ready to use.${NC}"

# Security reminder
echo -e "\n${YELLOW}🔒 Security Reminder:${NC}"
echo "- Never commit your .env file to version control"
echo "- Keep your API keys secure and rotate them regularly"
echo "- Use environment variables in production deployments"