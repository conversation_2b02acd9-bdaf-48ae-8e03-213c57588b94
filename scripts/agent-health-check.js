#!/usr/bin/env node

/**
 * Health Check Script for LiveKit Agent
 * Used by Docker and Kubernetes health checks
 */

import fs from "fs";
import path from "path";
import { config } from "dotenv";

config();

const HEALTH_FILE = "/tmp/agent-health";
const MAX_INACTIVITY = 300000; // 5 minutes in milliseconds

function checkAgentHealth() {
  return new Promise((resolve, reject) => {
    try {
      // Check if health file exists
      if (!fs.existsSync(HEALTH_FILE)) {
        console.log("⚠️ Health file not found, agent may be starting");
        // Allow some time for agent to start
        resolve(true);
        return;
      }

      // Read health file
      const healthData = fs.readFileSync(HEALTH_FILE, "utf8");
      const health = JSON.parse(healthData);

      const now = Date.now();
      const lastActivity = health.lastActivity || 0;
      const timeSinceActivity = now - lastActivity;

      // Check if agent is active
      if (timeSinceActivity > MAX_INACTIVITY) {
        console.error(
          `❌ Agent inactive for ${Math.round(timeSinceActivity / 1000)}s`
        );
        reject(new Error("Agent inactive too long"));
        return;
      }

      // Check agent status
      if (health.status === "error") {
        console.error(
          "❌ Agent in error state:",
          health.errorDetails?.message || "Unknown error"
        );
        reject(new Error("Agent in error state"));
        return;
      }

      if (health.status === "initializing") {
        console.log("⏳ Agent is initializing...");
        resolve(true);
        return;
      }

      if (health.status === "connected" || health.status === "processing") {
        console.log(`✅ Agent healthy (${health.status})`);
        resolve(true);
        return;
      }

      console.log(`ℹ️ Agent status: ${health.status}`);
      resolve(true);
    } catch (error) {
      console.error("❌ Health check error:", error.message);
      reject(error);
    }
  });
}

// Alternative health check: Check process and memory
function checkProcessHealth() {
  return new Promise((resolve, reject) => {
    try {
      const memUsage = process.memoryUsage();
      const memUsageMB = Math.round(memUsage.rss / 1024 / 1024);

      // Check memory usage (warn if over 1GB)
      if (memUsageMB > 1024) {
        console.warn(`⚠️ High memory usage: ${memUsageMB}MB`);
      }

      // Check uptime
      const uptimeSeconds = process.uptime();
      console.log(
        `ℹ️ Process uptime: ${Math.round(
          uptimeSeconds
        )}s, Memory: ${memUsageMB}MB`
      );

      resolve(true);
    } catch (error) {
      reject(error);
    }
  });
}

// Run health checks
async function runHealthChecks() {
  try {
    // Try agent-specific health check first
    await checkAgentHealth();
  } catch (error) {
    console.warn("Agent health check failed, trying process health check");

    try {
      // Fallback to process health check
      await checkProcessHealth();
    } catch (processError) {
      console.error("All health checks failed");
      throw processError;
    }
  }
}

// Execute health check
runHealthChecks()
  .then(() => {
    console.log("✅ Agent health check passed");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Agent health check failed:", error.message);
    process.exit(1);
  });
