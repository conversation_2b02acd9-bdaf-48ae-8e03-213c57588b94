#!/usr/bin/env node

/**
 * Health Check Script for Web Server
 * Used by Docker and Kubernetes health checks
 */

import http from "http";
import { config } from "dotenv";

config();

const PORT = process.env.PORT || 3000;
const TIMEOUT = 5000; // 5 seconds

function healthCheck() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: "localhost",
      port: PORT,
      path: "/api/health",
      method: "GET",
      timeout: TIMEOUT,
    };

    const req = http.request(options, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        if (res.statusCode === 200) {
          try {
            const healthData = JSON.parse(data);
            if (healthData.status === "ok") {
              console.log("✅ Health check passed");
              resolve(true);
            } else {
              console.error("❌ Health check failed: Invalid status");
              reject(new Error("Invalid health status"));
            }
          } catch (error) {
            console.error("❌ Health check failed: Invalid JSON response");
            reject(error);
          }
        } else {
          console.error(`❌ Health check failed: HTTP ${res.statusCode}`);
          reject(new Error(`HTTP ${res.statusCode}`));
        }
      });
    });

    req.on("error", (error) => {
      console.error("❌ Health check failed: Connection error", error.message);
      reject(error);
    });

    req.on("timeout", () => {
      req.destroy();
      console.error("❌ Health check failed: Timeout");
      reject(new Error("Timeout"));
    });

    req.end();
  });
}

// Run health check
healthCheck()
  .then(() => {
    process.exit(0);
  })
  .catch((error) => {
    console.error("Health check failed:", error.message);
    process.exit(1);
  });
