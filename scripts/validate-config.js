#!/usr/bin/env node

/**
 * Standalone Configuration Validation Script
 * Can be run independently to validate system configuration
 */

import { config } from "dotenv";
import { ConfigValidator } from "../src/config-validator.js";
import { fileURLToPath } from "node:url";
import { dirname, join } from "node:path";

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, "..");

// Try to load .env file from project root
config({ path: join(projectRoot, ".env") });

async function main() {
  console.log("🚀 LiveKit Agents + Deepgram Configuration Validator");
  console.log("=".repeat(60));

  // Parse command line arguments
  const args = process.argv.slice(2);
  const options = {
    skipConnectivityTests: args.includes("--skip-connectivity"),
    timeout: 15000, // Longer timeout for standalone validation
    retries: 3,
  };

  if (args.includes("--help") || args.includes("-h")) {
    printHelp();
    process.exit(0);
  }

  if (options.skipConnectivityTests) {
    console.log(
      "⚠️  Skipping connectivity tests (--skip-connectivity flag detected)"
    );
  }

  try {
    // Run comprehensive validation
    const results = await ConfigValidator.quickValidate(options);

    // Exit with appropriate code
    if (results.overall) {
      console.log("\n🎉 Configuration validation completed successfully!");
      console.log("You can now start the LiveKit agent with: npm run agent");
      process.exit(0);
    } else {
      console.log("\n❌ Configuration validation failed!");
      console.log("Please fix the issues above before starting the agent.");
      process.exit(1);
    }
  } catch (error) {
    console.error("\n💥 Validation script error:", error.message);
    if (process.env.NODE_ENV === "development") {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

function printHelp() {
  console.log(`
Usage: node scripts/validate-config.js [options]

Options:
  --skip-connectivity    Skip API connectivity tests (faster, but less thorough)
  --help, -h            Show this help message

Examples:
  node scripts/validate-config.js                    # Full validation
  node scripts/validate-config.js --skip-connectivity # Skip connectivity tests
  npm run validate                                   # Using npm script

Environment Setup:
  1. Copy .env.example to .env
  2. Fill in your API keys and configuration
  3. Run this validation script
  4. Start the agent with: npm run agent

Required Environment Variables:
  LIVEKIT_URL          - Your LiveKit server URL
  LIVEKIT_API_KEY      - LiveKit API key
  LIVEKIT_API_SECRET   - LiveKit API secret
  DEEPGRAM_API_KEY     - Deepgram API key

Optional Environment Variables:
  OPENAI_API_KEY       - OpenAI API key (for enhanced features)
  NODE_ENV             - Environment (development/production)
  PORT                 - Server port (default: 3000)
  LOG_LEVEL            - Logging level (error/warn/info/debug)

For more information, visit:
  - LiveKit: https://docs.livekit.io/
  - Deepgram: https://developers.deepgram.com/
  - Project Documentation: README.md
`);
}

// Handle unhandled rejections
process.on("unhandledRejection", (reason, promise) => {
  console.error("💥 Unhandled Promise Rejection:", reason);
  process.exit(1);
});

process.on("uncaughtException", (error) => {
  console.error("💥 Uncaught Exception:", error.message);
  process.exit(1);
});

// Run the validation
main();
